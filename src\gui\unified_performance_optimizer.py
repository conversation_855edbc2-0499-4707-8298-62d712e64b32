#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一配置界面性能优化和稳定性改进模块

包含以下优化功能：
- 数据加载优化（异步/分页）
- 内存使用优化
- UI响应性改进
- 错误恢复机制
- 缓存策略

创建时间: 2025-01-20
方案: 统一配置界面详细设计（方案3）- 阶段2实施
"""

import sys
import os
import gc
import time
import threading
from typing import Dict, List, Optional, Any, Callable
from functools import lru_cache, wraps
from PyQt5.QtCore import QObject, QThread, pyqtSignal, QTimer, QMutex
from PyQt5.QtWidgets import QApplication

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from src.utils.log_config import setup_logger


class PerformanceOptimizer:
    """性能优化器"""
    
    def __init__(self):
        self.logger = setup_logger(__name__)
        self.cache_enabled = True
        self.lazy_loading_enabled = True
        self.max_memory_usage_mb = 512  # 最大内存使用量（MB）
        
        # 性能监控数据
        self.performance_stats = {
            "data_load_times": [],
            "ui_response_times": [],
            "memory_usage": [],
            "cache_hits": 0,
            "cache_misses": 0
        }
    
    def enable_lazy_loading(self, enabled: bool = True):
        """启用或禁用懒加载"""
        self.lazy_loading_enabled = enabled
        self.logger.info(f"💤 懒加载模式: {'启用' if enabled else '禁用'}")
    
    def set_memory_limit(self, limit_mb: int):
        """设置内存使用限制"""
        self.max_memory_usage_mb = limit_mb
        self.logger.info(f"🎯 内存限制设置为: {limit_mb}MB")
    
    def monitor_memory_usage(self) -> float:
        """监控内存使用情况"""
        try:
            import psutil
            process = psutil.Process()
            memory_mb = process.memory_info().rss / 1024 / 1024
            self.performance_stats["memory_usage"].append(memory_mb)
            
            if memory_mb > self.max_memory_usage_mb:
                self.logger.warning(f"⚠️ 内存使用超限: {memory_mb:.1f}MB > {self.max_memory_usage_mb}MB")
                self._trigger_memory_cleanup()
            
            return memory_mb
        except ImportError:
            self.logger.warning("⚠️ psutil模块未安装，无法监控内存使用")
            return 0.0
        except Exception as e:
            self.logger.error(f"❌ 监控内存使用失败: {e}")
            return 0.0
    
    def _trigger_memory_cleanup(self):
        """触发内存清理"""
        try:
            # 强制垃圾回收
            collected = gc.collect()
            self.logger.info(f"🧹 垃圾回收完成，清理了 {collected} 个对象")
            
            # 清理缓存（保留最近使用的）
            self._cleanup_caches()
            
        except Exception as e:
            self.logger.error(f"❌ 内存清理失败: {e}")
    
    def _cleanup_caches(self):
        """清理缓存"""
        # 这里可以清理各种缓存
        # 例如：清理 LRU 缓存等
        self.logger.info("🧹 缓存清理完成")


class DataLoadOptimizer:
    """数据加载优化器"""
    
    def __init__(self, performance_optimizer: PerformanceOptimizer):
        self.logger = setup_logger(__name__)
        self.perf_optimizer = performance_optimizer
        self.page_size = 1000  # 分页大小
        self.cache = {}
        self.cache_mutex = QMutex()
    
    @lru_cache(maxsize=128)
    def cached_excel_columns(self, file_path: str, sheet_name: str) -> List[str]:
        """缓存Excel列名"""
        try:
            from src.modules.data_import.excel_importer import ExcelImporter
            
            start_time = time.time()
            importer = ExcelImporter()
            data = importer.import_excel(file_path, sheet_name, max_rows=1)
            
            if data is not None and not data.empty:
                columns = list(data.columns)
                load_time = time.time() - start_time
                self.perf_optimizer.performance_stats["data_load_times"].append(load_time)
                self.perf_optimizer.performance_stats["cache_hits"] += 1
                self.logger.debug(f"📊 Excel列名缓存命中: {sheet_name} ({load_time:.3f}s)")
                return columns
            
            return []
            
        except Exception as e:
            self.perf_optimizer.performance_stats["cache_misses"] += 1
            self.logger.error(f"❌ 缓存Excel列名失败: {e}")
            return []
    
    def load_excel_data_paginated(self, file_path: str, sheet_name: str, 
                                  page: int = 0, page_size: int = None) -> Optional[Any]:
        """分页加载Excel数据"""
        try:
            from src.modules.data_import.excel_importer import ExcelImporter
            
            page_size = page_size or self.page_size
            skip_rows = page * page_size
            
            start_time = time.time()
            importer = ExcelImporter()
            data = importer.import_excel(file_path, sheet_name, 
                                       skip_rows=skip_rows, max_rows=page_size)
            
            load_time = time.time() - start_time
            self.perf_optimizer.performance_stats["data_load_times"].append(load_time)
            
            self.logger.debug(f"📊 分页数据加载完成: {sheet_name} 第{page+1}页 ({load_time:.3f}s)")
            return data
            
        except Exception as e:
            self.logger.error(f"❌ 分页加载数据失败: {e}")
            return None
    
    def preload_data_async(self, file_path: str, sheet_names: List[str], 
                          callback: Callable = None):
        """异步预加载数据"""
        def preload_worker():
            try:
                for sheet_name in sheet_names:
                    # 预加载列名
                    self.cached_excel_columns(file_path, sheet_name)
                    
                    # 预加载第一页数据
                    self.load_excel_data_paginated(file_path, sheet_name, 0)
                    
                    self.logger.debug(f"📊 预加载完成: {sheet_name}")
                
                if callback:
                    callback()
                    
            except Exception as e:
                self.logger.error(f"❌ 异步预加载失败: {e}")
        
        # 在新线程中执行预加载
        thread = threading.Thread(target=preload_worker, daemon=True)
        thread.start()
        self.logger.info(f"🚀 开始异步预加载 {len(sheet_names)} 个Sheet")


class UIResponseOptimizer:
    """UI响应性优化器"""
    
    def __init__(self, performance_optimizer: PerformanceOptimizer):
        self.logger = setup_logger(__name__)
        self.perf_optimizer = performance_optimizer
        self.debounce_timers = {}
    
    def debounce(self, key: str, delay_ms: int = 300):
        """防抖装饰器"""
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                # 取消之前的定时器
                if key in self.debounce_timers:
                    self.debounce_timers[key].stop()
                
                # 创建新的定时器
                timer = QTimer()
                timer.timeout.connect(lambda: func(*args, **kwargs))
                timer.setSingleShot(True)
                timer.start(delay_ms)
                
                self.debounce_timers[key] = timer
                
            return wrapper
        return decorator
    
    def throttle(self, key: str, min_interval_ms: int = 100):
        """节流装饰器"""
        last_call_time = {}
        
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                current_time = time.time() * 1000
                last_time = last_call_time.get(key, 0)
                
                if current_time - last_time >= min_interval_ms:
                    last_call_time[key] = current_time
                    return func(*args, **kwargs)
                    
            return wrapper
        return decorator
    
    def measure_ui_response_time(self, operation_name: str):
        """测量UI响应时间装饰器"""
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                start_time = time.time()
                
                try:
                    result = func(*args, **kwargs)
                    response_time = time.time() - start_time
                    
                    self.perf_optimizer.performance_stats["ui_response_times"].append(response_time)
                    
                    if response_time > 0.1:  # 超过100ms记录警告
                        self.logger.warning(f"⚠️ UI响应较慢: {operation_name} ({response_time:.3f}s)")
                    else:
                        self.logger.debug(f"⚡ UI响应: {operation_name} ({response_time:.3f}s)")
                    
                    return result
                    
                except Exception as e:
                    response_time = time.time() - start_time
                    self.logger.error(f"❌ UI操作失败: {operation_name} ({response_time:.3f}s) - {e}")
                    raise
                    
            return wrapper
        return decorator


class StabilityManager:
    """稳定性管理器"""
    
    def __init__(self):
        self.logger = setup_logger(__name__)
        self.error_recovery_enabled = True
        self.auto_save_enabled = True
        self.auto_save_interval = 30  # 秒
    
    def enable_error_recovery(self, enabled: bool = True):
        """启用错误恢复"""
        self.error_recovery_enabled = enabled
        self.logger.info(f"🛡️ 错误恢复: {'启用' if enabled else '禁用'}")
    
    def safe_execute(self, func: Callable, fallback_func: Callable = None, 
                     error_message: str = "操作失败"):
        """安全执行函数"""
        def decorator(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                self.logger.error(f"❌ {error_message}: {e}")
                
                if self.error_recovery_enabled and fallback_func:
                    try:
                        self.logger.info(f"🔄 尝试错误恢复...")
                        return fallback_func(*args, **kwargs)
                    except Exception as recovery_error:
                        self.logger.error(f"❌ 错误恢复失败: {recovery_error}")
                
                raise e
        
        return decorator
    
    def with_retry(self, max_retries: int = 3, delay: float = 1.0):
        """重试装饰器"""
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                for attempt in range(max_retries + 1):
                    try:
                        return func(*args, **kwargs)
                    except Exception as e:
                        if attempt == max_retries:
                            self.logger.error(f"❌ 操作失败，已重试{max_retries}次: {e}")
                            raise
                        else:
                            self.logger.warning(f"⚠️ 操作失败，第{attempt+1}次重试: {e}")
                            time.sleep(delay)
                            
            return wrapper
        return decorator


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, performance_optimizer: PerformanceOptimizer):
        self.logger = setup_logger(__name__)
        self.perf_optimizer = performance_optimizer
        self.monitoring_enabled = True
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self._collect_metrics)
    
    def start_monitoring(self, interval_ms: int = 5000):
        """开始性能监控"""
        if self.monitoring_enabled:
            self.monitor_timer.start(interval_ms)
            self.logger.info(f"📊 性能监控已启动，间隔: {interval_ms}ms")
    
    def stop_monitoring(self):
        """停止性能监控"""
        self.monitor_timer.stop()
        self.logger.info("📊 性能监控已停止")
    
    def _collect_metrics(self):
        """收集性能指标"""
        try:
            # 监控内存使用
            memory_mb = self.perf_optimizer.monitor_memory_usage()
            
            # 收集其他指标
            stats = self.perf_optimizer.performance_stats
            
            self.logger.debug(f"📊 性能指标 - "
                            f"内存: {memory_mb:.1f}MB, "
                            f"缓存命中率: {self._calculate_cache_hit_rate():.1f}%, "
                            f"平均UI响应: {self._calculate_avg_ui_response():.3f}s")
            
        except Exception as e:
            self.logger.error(f"❌ 收集性能指标失败: {e}")
    
    def _calculate_cache_hit_rate(self) -> float:
        """计算缓存命中率"""
        stats = self.perf_optimizer.performance_stats
        total = stats["cache_hits"] + stats["cache_misses"]
        if total == 0:
            return 0.0
        return (stats["cache_hits"] / total) * 100
    
    def _calculate_avg_ui_response(self) -> float:
        """计算平均UI响应时间"""
        response_times = self.perf_optimizer.performance_stats["ui_response_times"]
        if not response_times:
            return 0.0
        return sum(response_times[-10:]) / min(len(response_times), 10)  # 最近10次的平均值
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        stats = self.perf_optimizer.performance_stats
        return {
            "memory_usage_mb": self.perf_optimizer.monitor_memory_usage(),
            "cache_hit_rate": self._calculate_cache_hit_rate(),
            "avg_ui_response_time": self._calculate_avg_ui_response(),
            "total_data_loads": len(stats["data_load_times"]),
            "avg_data_load_time": sum(stats["data_load_times"][-10:]) / min(len(stats["data_load_times"]), 10) if stats["data_load_times"] else 0,
            "total_ui_operations": len(stats["ui_response_times"])
        }


# 全局性能优化器实例
_performance_optimizer = None
_data_load_optimizer = None
_ui_response_optimizer = None
_stability_manager = None
_performance_monitor = None

def get_performance_optimizer() -> PerformanceOptimizer:
    """获取全局性能优化器实例"""
    global _performance_optimizer
    if _performance_optimizer is None:
        _performance_optimizer = PerformanceOptimizer()
    return _performance_optimizer

def get_data_load_optimizer() -> DataLoadOptimizer:
    """获取数据加载优化器实例"""
    global _data_load_optimizer
    if _data_load_optimizer is None:
        _data_load_optimizer = DataLoadOptimizer(get_performance_optimizer())
    return _data_load_optimizer

def get_ui_response_optimizer() -> UIResponseOptimizer:
    """获取UI响应优化器实例"""
    global _ui_response_optimizer
    if _ui_response_optimizer is None:
        _ui_response_optimizer = UIResponseOptimizer(get_performance_optimizer())
    return _ui_response_optimizer

def get_stability_manager() -> StabilityManager:
    """获取稳定性管理器实例"""
    global _stability_manager
    if _stability_manager is None:
        _stability_manager = StabilityManager()
    return _stability_manager

def get_performance_monitor() -> PerformanceMonitor:
    """获取性能监控器实例"""
    global _performance_monitor
    if _performance_monitor is None:
        _performance_monitor = PerformanceMonitor(get_performance_optimizer())
    return _performance_monitor


if __name__ == "__main__":
    """测试性能优化系统"""
    import time
    
    print("🚀 测试性能优化系统")
    print("=" * 40)
    
    # 测试性能优化器
    print("1. 测试性能优化器...")
    perf_optimizer = get_performance_optimizer()
    perf_optimizer.set_memory_limit(256)
    memory_usage = perf_optimizer.monitor_memory_usage()
    print(f"   📊 当前内存使用: {memory_usage:.1f}MB")
    
    # 测试数据加载优化器
    print("2. 测试数据加载优化器...")
    data_optimizer = get_data_load_optimizer()
    # data_optimizer.preload_data_async("test.xlsx", ["Sheet1"])
    print("   ✅ 数据加载优化器初始化完成")
    
    # 测试UI响应优化器
    print("3. 测试UI响应优化器...")
    ui_optimizer = get_ui_response_optimizer()
    
    @ui_optimizer.measure_ui_response_time("测试操作")
    def test_operation():
        time.sleep(0.05)  # 模拟50ms操作
        return "完成"
    
    result = test_operation()
    print(f"   ⚡ 测试操作结果: {result}")
    
    # 测试稳定性管理器
    print("4. 测试稳定性管理器...")
    stability_mgr = get_stability_manager()
    
    @stability_mgr.with_retry(max_retries=2)
    def test_retry_operation():
        return "成功"
    
    result = test_retry_operation()
    print(f"   🛡️ 重试操作结果: {result}")
    
    # 测试性能监控器
    print("5. 测试性能监控器...")
    monitor = get_performance_monitor()
    report = monitor.get_performance_report()
    print(f"   📊 性能报告: {report}")
    
    print()
    print("🎉 性能优化系统测试完成！")
