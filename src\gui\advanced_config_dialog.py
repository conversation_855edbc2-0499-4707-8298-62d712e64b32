"""
高级配置对话框
提供个性化设置、数据处理选项等高级配置功能
"""

import json
import os
from typing import Dict, Any
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTabWidget, QWidget, 
    QGroupBox, QFormLayout, QGridLayout, QLineEdit, QCheckBox, QComboBox, 
    QSpinBox, QSlider, QLabel, QPushButton, QMessageBox,
    QFileDialog, QTextEdit
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont

from src.modules.logging.setup_logger import setup_logger


class AdvancedConfigDialog(QDialog):
    """高级配置对话框"""
    
    config_changed = pyqtSignal(dict)  # 配置变化信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        try:
            self.logger = setup_logger(__name__)
            self.logger.info("高级配置对话框开始初始化...")
            
            self.config_file = "config/advanced_settings.json"
            self.config = {}
            
            self.logger.info("开始初始化UI...")
            self._init_ui()
            
            self.logger.info("开始加载配置...")
            self._load_config()
            
            self.logger.info("开始连接信号...")
            self._connect_signals()
            
            self.logger.info("高级配置对话框初始化完成")
        except Exception as e:
            import traceback
            error_msg = f"高级配置对话框初始化失败: {e}"
            traceback_msg = f"详细错误: {traceback.format_exc()}"
            
            # 同时输出到控制台和日志文件
            print(error_msg)
            print(traceback_msg)
            
            if hasattr(self, 'logger'):
                self.logger.error(error_msg)
                self.logger.error(traceback_msg)
            else:
                # 如果日志系统也失败了，至少保证控制台有输出
                print("警告: 日志系统不可用，仅输出到控制台")
            raise
    
    def _init_ui(self):
        """初始化UI"""
        self.setWindowTitle("高级配置")
        self.setFixedSize(800, 650)  # 增加窗口尺寸以适应所有内容
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(10)
        
        # 标题
        title_label = QLabel("🔧 高级配置设置")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)
        
        # 选项卡
        self.tab_widget = QTabWidget()
        
        # 文件导入设置
        self.file_tab = self._create_file_import_tab()
        self.tab_widget.addTab(self.file_tab, "📄 文件导入")
        
        # 字段映射设置
        self.mapping_tab = self._create_field_mapping_tab()
        self.tab_widget.addTab(self.mapping_tab, "🚨 字段映射")
        
        # 智能推荐设置
        self.smart_tab = self._create_smart_recommendations_tab()
        self.tab_widget.addTab(self.smart_tab, "🤖 智能推荐")
        
        # 数据处理设置
        self.data_tab = self._create_data_processing_tab()
        self.tab_widget.addTab(self.data_tab, "📊 数据处理")
        
        # 界面个性化
        self.ui_tab = self._create_ui_customization_tab()
        self.tab_widget.addTab(self.ui_tab, "🎨 界面设置")
        
        # 性能优化
        self.perf_tab = self._create_performance_tab()
        self.tab_widget.addTab(self.perf_tab, "⚡ 性能优化")
        
        main_layout.addWidget(self.tab_widget)
        
        # 底部按钮
        button_layout = self._create_bottom_buttons()
        main_layout.addLayout(button_layout)
    
    def _create_smart_recommendations_tab(self) -> QWidget:
        """创建智能推荐设置选项卡"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(15)
        
        # 映射推荐设置
        mapping_group = QGroupBox("映射推荐设置")
        mapping_layout = QFormLayout(mapping_group)
        
        # 推荐置信度阈值
        self.confidence_threshold = QSlider(Qt.Horizontal)
        self.confidence_threshold.setRange(50, 95)
        self.confidence_threshold.setValue(70)
        self.confidence_threshold.setTickPosition(QSlider.TicksBelow)
        self.confidence_threshold.setTickInterval(10)
        
        confidence_layout = QHBoxLayout()
        confidence_layout.addWidget(self.confidence_threshold)
        self.confidence_label = QLabel("70%")
        confidence_layout.addWidget(self.confidence_label)
        
        mapping_layout.addRow("推荐置信度阈值:", confidence_layout)
        
        # 历史学习开关
        self.enable_history_learning = QCheckBox("启用历史学习")
        self.enable_history_learning.setChecked(True)
        mapping_layout.addRow("", self.enable_history_learning)
        
        # 语义分析开关
        self.enable_semantic_analysis = QCheckBox("启用语义分析")
        self.enable_semantic_analysis.setChecked(True)
        mapping_layout.addRow("", self.enable_semantic_analysis)
        
        # 自动应用高置信度推荐
        self.auto_apply_high_confidence = QCheckBox("自动应用高置信度推荐")
        self.auto_apply_high_confidence.setChecked(False)
        mapping_layout.addRow("", self.auto_apply_high_confidence)
        
        layout.addWidget(mapping_group)
        
        # 模板管理设置
        template_group = QGroupBox("模板管理设置")
        template_layout = QFormLayout(template_group)
        
        # 模板推荐优先级
        self.template_priority = QComboBox()
        self.template_priority.addItems([
            "优先用户模板", "优先系统模板", "混合推荐"
        ])
        template_layout.addRow("模板推荐优先级:", self.template_priority)
        
        # 最大保存模板数
        self.max_saved_templates = QSpinBox()
        self.max_saved_templates.setRange(10, 100)
        self.max_saved_templates.setValue(50)
        template_layout.addRow("最大保存模板数:", self.max_saved_templates)
        
        layout.addWidget(template_group)
        
        layout.addStretch()
        return tab
    
    def _create_file_import_tab(self) -> QWidget:
        """创建文件导入设置选项卡"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(12)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # 文件路径设置
        path_group = QGroupBox("文件路径设置")
        path_layout = QFormLayout(path_group)
        path_layout.setSpacing(8)
        
        # 默认导入路径
        self.default_import_path = QLineEdit()
        self.default_import_path.setPlaceholderText("选择默认导入文件夹")
        self.default_import_path.setMinimumHeight(28)
        path_layout.addRow("默认导入路径:", self.default_import_path)
        
        layout.addWidget(path_group)
        
        # 文件格式设置
        format_group = QGroupBox("文件格式设置")
        format_layout = QVBoxLayout(format_group)  # 改为垂直布局
        format_layout.setSpacing(6)
        
        # 支持的文件格式 - 使用水平布局
        formats_layout = QHBoxLayout()
        self.support_xlsx = QCheckBox("Excel 2007+ (.xlsx)")
        self.support_xlsx.setChecked(True)
        self.support_xls = QCheckBox("Excel 97-2003 (.xls)")
        self.support_xls.setChecked(True)
        self.support_csv = QCheckBox("逗号分隔值 (.csv)")
        self.support_csv.setChecked(True)
        
        formats_layout.addWidget(self.support_xlsx)
        formats_layout.addWidget(self.support_xls)
        formats_layout.addWidget(self.support_csv)
        formats_layout.addStretch()
        
        format_layout.addLayout(formats_layout)
        
        # 文件大小限制
        size_layout = QHBoxLayout()
        size_layout.addWidget(QLabel("最大文件大小:"))
        self.max_file_size = QSpinBox()
        self.max_file_size.setRange(1, 1000)
        self.max_file_size.setValue(100)
        self.max_file_size.setSuffix(" MB")
        self.max_file_size.setMaximumWidth(120)
        size_layout.addWidget(self.max_file_size)
        size_layout.addStretch()
        
        format_layout.addLayout(size_layout)
        layout.addWidget(format_group)
        
        # 编码设置
        encoding_group = QGroupBox("编码设置")
        encoding_layout = QVBoxLayout(encoding_group)
        encoding_layout.setSpacing(8)
        
        # 自动检测编码
        self.auto_detect_encoding = QCheckBox("自动检测文件编码")
        self.auto_detect_encoding.setChecked(True)
        encoding_layout.addWidget(self.auto_detect_encoding)
        
        # Sheet选择策略
        sheet_layout = QHBoxLayout()
        sheet_layout.addWidget(QLabel("工作表选择策略:"))
        self.sheet_selection_strategy = QComboBox()
        self.sheet_selection_strategy.addItems([
            "自动选择全部", "仅选择第一个", "手动选择"
        ])
        self.sheet_selection_strategy.setMaximumWidth(150)
        sheet_layout.addWidget(self.sheet_selection_strategy)
        sheet_layout.addStretch()
        
        encoding_layout.addLayout(sheet_layout)
        layout.addWidget(encoding_group)
        
        # Excel表结构配置 - 优化布局
        excel_group = QGroupBox("Excel表结构配置")
        excel_layout = QGridLayout(excel_group)
        excel_layout.setSpacing(10)
        excel_layout.setColumnStretch(1, 1)  # 让第二列可伸缩
        excel_layout.setColumnStretch(3, 1)  # 让第四列可伸缩
        
        # 第一行：数据开始行和表头行
        excel_layout.addWidget(QLabel("数据开始行:"), 0, 0)
        self.data_start_row = QSpinBox()
        self.data_start_row.setRange(1, 100)
        self.data_start_row.setValue(2)
        self.data_start_row.setSuffix(" 行")
        self.data_start_row.setFixedWidth(90)
        excel_layout.addWidget(self.data_start_row, 0, 1, Qt.AlignLeft)
        
        excel_layout.addWidget(QLabel("表头所在行:"), 0, 2)
        self.header_row = QSpinBox()
        self.header_row.setRange(1, 100)
        self.header_row.setValue(1)
        self.header_row.setSuffix(" 行")
        self.header_row.setFixedWidth(90)
        excel_layout.addWidget(self.header_row, 0, 3, Qt.AlignLeft)
        
        # 第二行：跳过行数和数据结束行
        excel_layout.addWidget(QLabel("跳过前N行:"), 1, 0)
        self.skip_rows = QSpinBox()
        self.skip_rows.setRange(0, 50)
        self.skip_rows.setValue(0)
        self.skip_rows.setSuffix(" 行")
        self.skip_rows.setFixedWidth(90)
        excel_layout.addWidget(self.skip_rows, 1, 1, Qt.AlignLeft)
        
        excel_layout.addWidget(QLabel("忽略尾部N行:"), 1, 2)
        self.skip_footer_rows = QSpinBox()
        self.skip_footer_rows.setRange(0, 50)
        self.skip_footer_rows.setValue(0)
        self.skip_footer_rows.setSuffix(" 行")
        self.skip_footer_rows.setFixedWidth(90)
        self.skip_footer_rows.setToolTip("忽略表格末尾的汇总、备注等行")
        excel_layout.addWidget(self.skip_footer_rows, 1, 3, Qt.AlignLeft)
        
        # 第三行：智能选项（横跨多列）
        self.auto_detect_header = QCheckBox("智能检测表头位置")
        self.auto_detect_header.setChecked(True)
        excel_layout.addWidget(self.auto_detect_header, 2, 0, 1, 2)
        
        self.ignore_empty_rows = QCheckBox("忽略空白行")
        self.ignore_empty_rows.setChecked(True)
        excel_layout.addWidget(self.ignore_empty_rows, 2, 2, 1, 2)
        
        layout.addWidget(excel_group)
        
        layout.addStretch()
        return tab
    
    def _create_field_mapping_tab(self) -> QWidget:
        """创建字段映射设置选项卡"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(15)
        
        # 映射算法设置
        algorithm_group = QGroupBox("映射算法设置")
        algorithm_layout = QFormLayout(algorithm_group)
        
        # 映射算法类型
        self.mapping_algorithm = QComboBox()
        self.mapping_algorithm.addItems([
            "模糊匹配", "精确匹配", "语义匹配", "混合算法"
        ])
        algorithm_layout.addRow("映射算法类型:", self.mapping_algorithm)
        
        # 相似度阈值
        self.similarity_threshold = QSlider(Qt.Horizontal)
        self.similarity_threshold.setRange(50, 100)
        self.similarity_threshold.setValue(80)
        self.similarity_threshold.setTickPosition(QSlider.TicksBelow)
        self.similarity_threshold.setTickInterval(10)
        
        similarity_layout = QHBoxLayout()
        similarity_layout.addWidget(self.similarity_threshold)
        self.similarity_label = QLabel("80%")
        similarity_layout.addWidget(self.similarity_label)
        
        algorithm_layout.addRow("相似度阈值:", similarity_layout)
        
        layout.addWidget(algorithm_group)
        
        # 映射行为设置
        behavior_group = QGroupBox("映射行为设置")
        behavior_layout = QFormLayout(behavior_group)
        
        # 自动映射开关
        self.auto_mapping_enabled = QCheckBox("启用自动字段映射")
        self.auto_mapping_enabled.setChecked(True)
        behavior_layout.addRow("", self.auto_mapping_enabled)
        
        # 必填字段检查
        self.required_field_check = QCheckBox("检查必填字段完整性")
        self.required_field_check.setChecked(True)
        behavior_layout.addRow("", self.required_field_check)
        
        # 字段类型验证
        self.field_type_validation = QCheckBox("启用字段类型验证")
        self.field_type_validation.setChecked(True)
        behavior_layout.addRow("", self.field_type_validation)
        
        # 映射历史保存
        self.save_mapping_history = QCheckBox("保存映射历史记录")
        self.save_mapping_history.setChecked(True)
        behavior_layout.addRow("", self.save_mapping_history)
        
        layout.addWidget(behavior_group)
        layout.addStretch()
        return tab
    
    def _create_data_processing_tab(self) -> QWidget:
        """创建数据处理设置选项卡"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(15)
        
        # 数据验证设置
        validation_group = QGroupBox("数据验证设置")
        validation_layout = QFormLayout(validation_group)
        
        # 严格模式
        self.strict_validation = QCheckBox("启用严格验证模式")
        self.strict_validation.setChecked(False)
        validation_layout.addRow("", self.strict_validation)
        
        # 空值处理策略
        self.null_value_strategy = QComboBox()
        self.null_value_strategy.addItems([
            "保留空值", "转换为默认值", "跳过记录", "提示用户"
        ])
        validation_layout.addRow("空值处理策略:", self.null_value_strategy)
        
        # 数据类型自动转换
        self.auto_type_conversion = QCheckBox("启用数据类型自动转换")
        self.auto_type_conversion.setChecked(True)
        validation_layout.addRow("", self.auto_type_conversion)
        
        layout.addWidget(validation_group)
        
        # 导入行为设置
        import_group = QGroupBox("导入行为设置")
        import_layout = QFormLayout(import_group)
        
        # 重复数据处理
        self.duplicate_strategy = QComboBox()
        self.duplicate_strategy.addItems([
            "跳过重复", "覆盖旧数据", "保留全部", "提示用户"
        ])
        import_layout.addRow("重复数据处理:", self.duplicate_strategy)
        
        # 批量大小
        self.batch_size = QSpinBox()
        self.batch_size.setRange(100, 10000)
        self.batch_size.setValue(1000)
        self.batch_size.setSuffix(" 条/批")
        import_layout.addRow("批量处理大小:", self.batch_size)
        
        # 错误容忍度
        self.error_tolerance = QSlider(Qt.Horizontal)
        self.error_tolerance.setRange(0, 100)
        self.error_tolerance.setValue(10)
        self.error_tolerance.setTickPosition(QSlider.TicksBelow)
        self.error_tolerance.setTickInterval(20)
        
        tolerance_layout = QHBoxLayout()
        tolerance_layout.addWidget(self.error_tolerance)
        self.tolerance_label = QLabel("10%")
        tolerance_layout.addWidget(self.tolerance_label)
        
        import_layout.addRow("错误容忍度:", tolerance_layout)
        
        layout.addWidget(import_group)
        
        layout.addStretch()
        return tab
    
    def _create_ui_customization_tab(self) -> QWidget:
        """创建界面个性化设置选项卡"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(15)
        
        # 显示设置
        display_group = QGroupBox("显示设置")
        display_layout = QFormLayout(display_group)
        
        # 表格行数限制
        self.table_row_limit = QSpinBox()
        self.table_row_limit.setRange(50, 1000)
        self.table_row_limit.setValue(200)
        self.table_row_limit.setSuffix(" 行")
        display_layout.addRow("表格显示行数限制:", self.table_row_limit)
        
        # 显示详细日志
        self.show_detailed_logs = QCheckBox("显示详细操作日志")
        self.show_detailed_logs.setChecked(False)
        display_layout.addRow("", self.show_detailed_logs)
        
        # 显示置信度指示器
        self.show_confidence_indicators = QCheckBox("显示置信度指示器")
        self.show_confidence_indicators.setChecked(True)
        display_layout.addRow("", self.show_confidence_indicators)
        
        layout.addWidget(display_group)
        
        # 交互设置
        interaction_group = QGroupBox("交互设置")
        interaction_layout = QFormLayout(interaction_group)
        
        # 自动保存间隔
        self.auto_save_interval = QSpinBox()
        self.auto_save_interval.setRange(0, 60)
        self.auto_save_interval.setValue(5)
        self.auto_save_interval.setSuffix(" 分钟")
        self.auto_save_interval.setSpecialValueText("禁用")
        interaction_layout.addRow("自动保存间隔:", self.auto_save_interval)
        
        # 确认对话框
        self.show_confirmation_dialogs = QCheckBox("显示确认对话框")
        self.show_confirmation_dialogs.setChecked(True)
        interaction_layout.addRow("", self.show_confirmation_dialogs)
        
        # 快捷键提示
        self.show_shortcuts = QCheckBox("显示快捷键提示")
        self.show_shortcuts.setChecked(True)
        interaction_layout.addRow("", self.show_shortcuts)
        
        layout.addWidget(interaction_group)
        
        layout.addStretch()
        return tab
    
    def _create_performance_tab(self) -> QWidget:
        """创建性能优化设置选项卡"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(15)
        
        # 内存管理
        memory_group = QGroupBox("内存管理")
        memory_layout = QFormLayout(memory_group)
        
        # 最大内存使用
        self.max_memory_usage = QSlider(Qt.Horizontal)
        self.max_memory_usage.setRange(512, 8192)
        self.max_memory_usage.setValue(2048)
        self.max_memory_usage.setTickPosition(QSlider.TicksBelow)
        self.max_memory_usage.setTickInterval(1024)
        
        memory_layout_h = QHBoxLayout()
        memory_layout_h.addWidget(self.max_memory_usage)
        self.memory_label = QLabel("2048 MB")
        memory_layout_h.addWidget(self.memory_label)
        
        memory_layout.addRow("最大内存使用:", memory_layout_h)
        
        # 缓存设置
        self.enable_caching = QCheckBox("启用数据缓存")
        self.enable_caching.setChecked(True)
        memory_layout.addRow("", self.enable_caching)
        
        # 预加载数据
        self.preload_data = QCheckBox("预加载常用数据")
        self.preload_data.setChecked(False)
        memory_layout.addRow("", self.preload_data)
        
        layout.addWidget(memory_group)
        
        # 处理优化
        processing_group = QGroupBox("处理优化")
        processing_layout = QFormLayout(processing_group)
        
        # 线程数
        self.thread_count = QSpinBox()
        self.thread_count.setRange(1, 16)
        self.thread_count.setValue(4)
        processing_layout.addRow("处理线程数:", self.thread_count)
        
        # 异步处理
        self.enable_async_processing = QCheckBox("启用异步处理")
        self.enable_async_processing.setChecked(True)
        processing_layout.addRow("", self.enable_async_processing)
        
        # 进度反馈频率
        self.progress_update_frequency = QSpinBox()
        self.progress_update_frequency.setRange(10, 1000)
        self.progress_update_frequency.setValue(100)
        self.progress_update_frequency.setSuffix(" 毫秒")
        processing_layout.addRow("进度更新频率:", self.progress_update_frequency)
        
        layout.addWidget(processing_group)
        
        # 系统信息
        info_group = QGroupBox("系统信息")
        info_layout = QVBoxLayout(info_group)
        
        self.system_info = QTextEdit()
        self.system_info.setMaximumHeight(80)
        self.system_info.setReadOnly(True)
        self._update_system_info()
        
        info_layout.addWidget(self.system_info)
        layout.addWidget(info_group)
        
        layout.addStretch()
        return tab
    
    def _create_bottom_buttons(self) -> QHBoxLayout:
        """创建底部按钮"""
        layout = QHBoxLayout()
        
        # 重置按钮
        self.reset_btn = QPushButton("🔄 重置为默认")
        
        # 导入导出按钮
        self.export_btn = QPushButton("📤 导出配置")
        self.import_btn = QPushButton("📥 导入配置")
        
        # 确定取消按钮
        self.ok_btn = QPushButton("✅ 确定")
        self.cancel_btn = QPushButton("❌ 取消")
        
        layout.addWidget(self.reset_btn)
        layout.addStretch()
        layout.addWidget(self.export_btn)
        layout.addWidget(self.import_btn)
        layout.addWidget(self.ok_btn)
        layout.addWidget(self.cancel_btn)
        
        return layout
    
    def _connect_signals(self):
        """连接信号"""
        # 滑块值变化
        self.confidence_threshold.valueChanged.connect(
            lambda v: self.confidence_label.setText(f"{v}%")
        )
        self.error_tolerance.valueChanged.connect(
            lambda v: self.tolerance_label.setText(f"{v}%")
        )
        self.max_memory_usage.valueChanged.connect(
            lambda v: self.memory_label.setText(f"{v} MB")
        )
        
        # 新增字段映射滑块连接
        self.similarity_threshold.valueChanged.connect(
            lambda v: self.similarity_label.setText(f"{v}%")
        )
        
        # 按钮点击
        self.reset_btn.clicked.connect(self._reset_to_defaults)
        self.export_btn.clicked.connect(self._export_config)
        self.import_btn.clicked.connect(self._import_config)
        self.ok_btn.clicked.connect(self._save_and_close)
        self.cancel_btn.clicked.connect(self.reject)
    
    def _load_config(self):
        """加载配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
                    
                self._apply_config_to_ui()
                self.logger.info("高级配置加载成功")
            else:
                self._set_default_config()
                
        except Exception as e:
            self.logger.error(f"加载高级配置失败: {e}")
            self._set_default_config()
    
    def _set_default_config(self):
        """设置默认配置"""
        self.config = {
            'file_import': {
                'default_import_path': '',
                'supported_formats': ['xlsx', 'xls', 'csv'],
                'max_file_size_mb': 100,
                'auto_detect_encoding': True,
                'sheet_selection_strategy': 'all',
                # Excel表结构配置
                'data_start_row': 2,
                'header_row': 1,
                'skip_rows': 0,
                'skip_footer_rows': 0,
                'auto_detect_header': True,
                'ignore_empty_rows': True
            },
            'field_mapping': {
                'mapping_algorithm': 'fuzzy_match',
                'similarity_threshold': 80,
                'auto_mapping_enabled': True,
                'required_field_check': True,
                'field_type_validation': True,
                'save_mapping_history': True
            },
            'smart_recommendations': {
                'confidence_threshold': 70,
                'enable_history_learning': True,
                'enable_semantic_analysis': True,
                'auto_apply_high_confidence': False,
                'template_priority': 0,
                'max_saved_templates': 50
            },
            'data_processing': {
                'strict_validation': False,
                'null_value_strategy': 0,
                'auto_type_conversion': True,
                'duplicate_strategy': 0,
                'batch_size': 1000,
                'error_tolerance': 10
            },
            'ui_customization': {
                'table_row_limit': 200,
                'show_detailed_logs': False,
                'show_confidence_indicators': True,
                'auto_save_interval': 5,
                'show_confirmation_dialogs': True,
                'show_shortcuts': True
            },
            'performance': {
                'max_memory_usage': 2048,
                'enable_caching': True,
                'preload_data': False,
                'thread_count': 4,
                'enable_async_processing': True,
                'progress_update_frequency': 100
            }
        }
    
    def _apply_config_to_ui(self):
        """将配置应用到UI"""
        # 文件导入配置
        file_import = self.config.get('file_import', {})
        self.default_import_path.setText(file_import.get('default_import_path', ''))
        supported_formats = file_import.get('supported_formats', ['xlsx', 'xls', 'csv'])
        self.support_xlsx.setChecked('xlsx' in supported_formats)
        self.support_xls.setChecked('xls' in supported_formats)
        self.support_csv.setChecked('csv' in supported_formats)
        self.max_file_size.setValue(file_import.get('max_file_size_mb', 100))
        self.auto_detect_encoding.setChecked(file_import.get('auto_detect_encoding', True))
        strategy_map = {'all': 0, 'first': 1, 'manual': 2}
        strategy = file_import.get('sheet_selection_strategy', 'all')
        self.sheet_selection_strategy.setCurrentIndex(strategy_map.get(strategy, 0))
        
        # Excel表结构配置
        self.data_start_row.setValue(file_import.get('data_start_row', 2))
        self.header_row.setValue(file_import.get('header_row', 1))
        self.skip_rows.setValue(file_import.get('skip_rows', 0))
        self.skip_footer_rows.setValue(file_import.get('skip_footer_rows', 0))
        self.auto_detect_header.setChecked(file_import.get('auto_detect_header', True))
        self.ignore_empty_rows.setChecked(file_import.get('ignore_empty_rows', True))
        
        # 字段映射配置
        field_mapping = self.config.get('field_mapping', {})
        algorithm_map = {'fuzzy_match': 0, 'exact_match': 1, 'semantic_match': 2, 'hybrid': 3}
        algorithm = field_mapping.get('mapping_algorithm', 'fuzzy_match')
        self.mapping_algorithm.setCurrentIndex(algorithm_map.get(algorithm, 0))
        self.similarity_threshold.setValue(field_mapping.get('similarity_threshold', 80))
        self.auto_mapping_enabled.setChecked(field_mapping.get('auto_mapping_enabled', True))
        self.required_field_check.setChecked(field_mapping.get('required_field_check', True))
        self.field_type_validation.setChecked(field_mapping.get('field_type_validation', True))
        self.save_mapping_history.setChecked(field_mapping.get('save_mapping_history', True))
        
        # 智能推荐配置
        smart = self.config.get('smart_recommendations', {})
        self.confidence_threshold.setValue(smart.get('confidence_threshold', 70))
        self.enable_history_learning.setChecked(smart.get('enable_history_learning', True))
        self.enable_semantic_analysis.setChecked(smart.get('enable_semantic_analysis', True))
        self.auto_apply_high_confidence.setChecked(smart.get('auto_apply_high_confidence', False))
        self.template_priority.setCurrentIndex(smart.get('template_priority', 0))
        self.max_saved_templates.setValue(smart.get('max_saved_templates', 50))
        
        # 数据处理配置
        data = self.config.get('data_processing', {})
        self.strict_validation.setChecked(data.get('strict_validation', False))
        self.null_value_strategy.setCurrentIndex(data.get('null_value_strategy', 0))
        self.auto_type_conversion.setChecked(data.get('auto_type_conversion', True))
        self.duplicate_strategy.setCurrentIndex(data.get('duplicate_strategy', 0))
        self.batch_size.setValue(data.get('batch_size', 1000))
        self.error_tolerance.setValue(data.get('error_tolerance', 10))
        
        # UI个性化配置
        ui = self.config.get('ui_customization', {})
        self.table_row_limit.setValue(ui.get('table_row_limit', 200))
        self.show_detailed_logs.setChecked(ui.get('show_detailed_logs', False))
        self.show_confidence_indicators.setChecked(ui.get('show_confidence_indicators', True))
        self.auto_save_interval.setValue(ui.get('auto_save_interval', 5))
        self.show_confirmation_dialogs.setChecked(ui.get('show_confirmation_dialogs', True))
        self.show_shortcuts.setChecked(ui.get('show_shortcuts', True))
        
        # 性能优化配置
        perf = self.config.get('performance', {})
        self.max_memory_usage.setValue(perf.get('max_memory_usage', 2048))
        self.enable_caching.setChecked(perf.get('enable_caching', True))
        self.preload_data.setChecked(perf.get('preload_data', False))
        self.thread_count.setValue(perf.get('thread_count', 4))
        self.enable_async_processing.setChecked(perf.get('enable_async_processing', True))
        self.progress_update_frequency.setValue(perf.get('progress_update_frequency', 100))
    
    def _collect_config_from_ui(self) -> Dict[str, Any]:
        """从UI收集配置"""
        # 获取支持的文件格式
        supported_formats = []
        if self.support_xlsx.isChecked():
            supported_formats.append('xlsx')
        if self.support_xls.isChecked():
            supported_formats.append('xls')
        if self.support_csv.isChecked():
            supported_formats.append('csv')
        
        # 工作表选择策略映射
        strategy_names = ['all', 'first', 'manual']
        sheet_strategy = strategy_names[self.sheet_selection_strategy.currentIndex()]
        
        # 映射算法映射
        algorithm_names = ['fuzzy_match', 'exact_match', 'semantic_match', 'hybrid']
        mapping_algorithm = algorithm_names[self.mapping_algorithm.currentIndex()]
        
        return {
            'file_import': {
                'default_import_path': self.default_import_path.text(),
                'supported_formats': supported_formats,
                'max_file_size_mb': self.max_file_size.value(),
                'auto_detect_encoding': self.auto_detect_encoding.isChecked(),
                'sheet_selection_strategy': sheet_strategy,
                # Excel表结构配置
                'data_start_row': self.data_start_row.value(),
                'header_row': self.header_row.value(),
                'skip_rows': self.skip_rows.value(),
                'skip_footer_rows': self.skip_footer_rows.value(),
                'auto_detect_header': self.auto_detect_header.isChecked(),
                'ignore_empty_rows': self.ignore_empty_rows.isChecked()
            },
            'field_mapping': {
                'mapping_algorithm': mapping_algorithm,
                'similarity_threshold': self.similarity_threshold.value(),
                'auto_mapping_enabled': self.auto_mapping_enabled.isChecked(),
                'required_field_check': self.required_field_check.isChecked(),
                'field_type_validation': self.field_type_validation.isChecked(),
                'save_mapping_history': self.save_mapping_history.isChecked()
            },
            'smart_recommendations': {
                'confidence_threshold': self.confidence_threshold.value(),
                'enable_history_learning': self.enable_history_learning.isChecked(),
                'enable_semantic_analysis': self.enable_semantic_analysis.isChecked(),
                'auto_apply_high_confidence': self.auto_apply_high_confidence.isChecked(),
                'template_priority': self.template_priority.currentIndex(),
                'max_saved_templates': self.max_saved_templates.value()
            },
            'data_processing': {
                'strict_validation': self.strict_validation.isChecked(),
                'null_value_strategy': self.null_value_strategy.currentIndex(),
                'auto_type_conversion': self.auto_type_conversion.isChecked(),
                'duplicate_strategy': self.duplicate_strategy.currentIndex(),
                'batch_size': self.batch_size.value(),
                'error_tolerance': self.error_tolerance.value()
            },
            'ui_customization': {
                'table_row_limit': self.table_row_limit.value(),
                'show_detailed_logs': self.show_detailed_logs.isChecked(),
                'show_confidence_indicators': self.show_confidence_indicators.isChecked(),
                'auto_save_interval': self.auto_save_interval.value(),
                'show_confirmation_dialogs': self.show_confirmation_dialogs.isChecked(),
                'show_shortcuts': self.show_shortcuts.isChecked()
            },
            'performance': {
                'max_memory_usage': self.max_memory_usage.value(),
                'enable_caching': self.enable_caching.isChecked(),
                'preload_data': self.preload_data.isChecked(),
                'thread_count': self.thread_count.value(),
                'enable_async_processing': self.enable_async_processing.isChecked(),
                'progress_update_frequency': self.progress_update_frequency.value()
            }
        }
    
    def _save_config(self):
        """保存配置"""
        try:
            # 确保配置目录存在
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            
            # 保存配置
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
                
            self.logger.info("高级配置保存成功")
            
        except Exception as e:
            self.logger.error(f"保存高级配置失败: {e}")
            QMessageBox.warning(self, "保存失败", f"配置保存失败: {e}")
    
    def _reset_to_defaults(self):
        """重置为默认设置"""
        reply = QMessageBox.question(
            self, "确认重置", 
            "确定要重置所有设置为默认值吗？",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self._set_default_config()
            self._apply_config_to_ui()
            self.logger.info("配置已重置为默认值")
    
    def _export_config(self):
        """导出配置"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出配置", 
            "advanced_config.json",
            "JSON Files (*.json)"
        )
        
        if file_path:
            try:
                current_config = self._collect_config_from_ui()
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(current_config, f, ensure_ascii=False, indent=2)
                
                QMessageBox.information(self, "导出成功", f"配置已导出到: {file_path}")
                self.logger.info(f"配置导出成功: {file_path}")
                
            except Exception as e:
                QMessageBox.warning(self, "导出失败", f"配置导出失败: {e}")
                self.logger.error(f"配置导出失败: {e}")
    
    def _import_config(self):
        """导入配置"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "导入配置",
            "",
            "JSON Files (*.json)"
        )
        
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    imported_config = json.load(f)
                
                self.config = imported_config
                self._apply_config_to_ui()
                
                QMessageBox.information(self, "导入成功", "配置导入成功")
                self.logger.info(f"配置导入成功: {file_path}")
                
            except Exception as e:
                QMessageBox.warning(self, "导入失败", f"配置导入失败: {e}")
                self.logger.error(f"配置导入失败: {e}")
    
    def _save_and_close(self):
        """保存配置并关闭"""
        self.config = self._collect_config_from_ui()
        self._save_config()
        
        # 发送配置变化信号
        self.config_changed.emit(self.config)
        
        self.accept()
    
    def _update_system_info(self):
        """更新系统信息"""
        import psutil
        import platform
        
        try:
            cpu_percent = psutil.cpu_percent()
            memory = psutil.virtual_memory()
            
            info_text = f"""系统: {platform.system()} {platform.release()}
CPU使用率: {cpu_percent}%
内存使用: {memory.percent}% ({memory.used // 1024**2}MB / {memory.total // 1024**2}MB)
Python版本: {platform.python_version()}"""
            
            self.system_info.setPlainText(info_text)
            
        except ImportError:
            self.system_info.setPlainText("系统信息获取需要安装psutil模块")
        except Exception as e:
            self.system_info.setPlainText(f"获取系统信息失败: {e}")
    
    def get_config(self) -> Dict[str, Any]:
        """获取当前配置"""
        return self.config.copy()


# 测试用主函数
if __name__ == "__main__":
    import sys
    from PyQt5.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    
    dialog = AdvancedConfigDialog()
    dialog.show()
    
    sys.exit(app.exec_())
