#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一配置管理器

负责管理统一配置界面中的所有配置数据，包括：
- 配置优先级管理
- 配置冲突检测和解析
- 配置数据的保存和加载
- 配置来源追踪

创建时间: 2025-01-20
方案: 统一配置界面详细设计（方案3）
"""

import json
import os
import sys
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple, Union
from enum import Enum
from dataclasses import dataclass, asdict
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from src.utils.log_config import setup_logger


class ConfigurationSource(Enum):
    """配置来源枚举"""
    SYSTEM_DEFAULT = "SYSTEM_DEFAULT"      # 系统默认配置
    TABLE_TEMPLATE = "TABLE_TEMPLATE"      # 表模板配置
    USER_CONFIG = "USER_CONFIG"            # 用户自定义配置
    TEMPORARY_OVERRIDE = "TEMPORARY_OVERRIDE"  # 临时覆盖配置


class ConfigurationPriority:
    """配置优先级管理"""
    
    # 优先级顺序（数字越小优先级越高）
    PRIORITY_ORDER = {
        ConfigurationSource.USER_CONFIG: 1,        # 最高优先级：用户自定义配置
        ConfigurationSource.TEMPORARY_OVERRIDE: 2, # 高优先级：临时覆盖配置
        ConfigurationSource.TABLE_TEMPLATE: 3,     # 中优先级：表模板配置
        ConfigurationSource.SYSTEM_DEFAULT: 4      # 低优先级：系统默认配置
    }
    
    @classmethod
    def get_priority(cls, source: ConfigurationSource) -> int:
        """获取配置来源的优先级"""
        return cls.PRIORITY_ORDER.get(source, 999)
    
    @classmethod
    def compare_priority(cls, source1: ConfigurationSource, source2: ConfigurationSource) -> int:
        """比较两个配置来源的优先级
        
        Returns:
            -1: source1 优先级更高
             0: 优先级相同
             1: source2 优先级更高
        """
        priority1 = cls.get_priority(source1)
        priority2 = cls.get_priority(source2)
        
        if priority1 < priority2:
            return -1
        elif priority1 > priority2:
            return 1
        else:
            return 0


@dataclass
class ConfigurationItem:
    """配置项数据结构"""
    key: str                                # 配置键
    value: Any                             # 配置值
    source: ConfigurationSource            # 配置来源
    timestamp: datetime                    # 创建/修改时间
    description: str = ""                  # 配置描述
    metadata: Dict[str, Any] = None       # 元数据
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['source'] = self.source.value
        data['timestamp'] = self.timestamp.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ConfigurationItem':
        """从字典创建配置项"""
        data = data.copy()
        data['source'] = ConfigurationSource(data['source'])
        data['timestamp'] = datetime.fromisoformat(data['timestamp'])
        return cls(**data)


@dataclass
class FieldMapping:
    """字段映射配置"""
    excel_field: str                       # Excel字段名
    system_field: str                      # 系统字段名
    field_type: str                        # 字段类型
    formatting_rules: Dict[str, Any]       # 格式化规则
    is_required: bool                      # 是否必填
    source: ConfigurationSource            # 配置来源
    timestamp: datetime                    # 时间戳
    validation_rules: Dict[str, Any] = None  # 验证规则
    
    def __post_init__(self):
        if self.validation_rules is None:
            self.validation_rules = {}
        if self.formatting_rules is None:
            self.formatting_rules = {}


@dataclass
class SheetConfiguration:
    """Sheet配置"""
    sheet_name: str                        # Sheet名称
    enabled: bool                          # 是否启用
    data_type: str                         # 数据类型
    field_mappings: List[FieldMapping]     # 字段映射列表
    metadata: Dict[str, Any] = None       # 元数据
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class ConflictReport:
    """配置冲突报告"""
    
    def __init__(self, field_key: str, configurations: List[ConfigurationItem]):
        self.field_key = field_key
        self.configurations = configurations
        self.timestamp = datetime.now()
    
    def get_winning_config(self) -> ConfigurationItem:
        """获取优胜的配置（优先级最高的）"""
        if not self.configurations:
            return None
        
        return min(self.configurations, 
                  key=lambda config: ConfigurationPriority.get_priority(config.source))
    
    def has_conflict(self) -> bool:
        """是否存在冲突"""
        return len(self.configurations) > 1
    
    def get_conflict_summary(self) -> str:
        """获取冲突摘要"""
        if not self.has_conflict():
            return "无冲突"
        
        sources = [config.source.value for config in self.configurations]
        winning = self.get_winning_config()
        
        return f"字段 '{self.field_key}' 存在 {len(sources)} 个配置源：{', '.join(sources)}，生效配置来源：{winning.source.value}"


class ConfigurationManager:
    """配置管理核心类"""
    
    def __init__(self):
        """初始化配置管理器"""
        self.logger = setup_logger(__name__)
        
        # 配置存储
        self.configurations: Dict[str, List[ConfigurationItem]] = {}
        self.sheet_configurations: Dict[str, SheetConfiguration] = {}
        self.conflict_reports: List[ConflictReport] = []
        
        # 配置文件路径
        self.config_dir = Path("state/unified_configs")
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # 默认配置文件
        self.user_config_file = self.config_dir / "user_configurations.json"
        self.template_config_file = self.config_dir / "template_configurations.json"
        self.system_config_file = self.config_dir / "system_defaults.json"
        
        # 加载现有配置
        self._load_configurations()
        
        self.logger.info("🔧 配置管理器初始化完成")
    
    def add_configuration(self, key: str, value: Any, source: ConfigurationSource, 
                         description: str = "", metadata: Dict[str, Any] = None) -> ConfigurationItem:
        """添加配置项"""
        config_item = ConfigurationItem(
            key=key,
            value=value,
            source=source,
            timestamp=datetime.now(),
            description=description,
            metadata=metadata or {}
        )
        
        # 添加到配置存储
        if key not in self.configurations:
            self.configurations[key] = []
        
        # 检查是否已存在相同来源的配置
        existing_configs = [c for c in self.configurations[key] if c.source == source]
        if existing_configs:
            # 更新现有配置
            existing_configs[0].value = value
            existing_configs[0].timestamp = datetime.now()
            existing_configs[0].description = description
            existing_configs[0].metadata.update(metadata or {})
            config_item = existing_configs[0]
        else:
            # 添加新配置
            self.configurations[key].append(config_item)
        
        # 更新冲突报告
        self._update_conflict_reports(key)
        
        self.logger.debug(f"📝 添加配置: {key} = {value} (来源: {source.value})")
        return config_item
    
    def get_effective_configuration(self, key: str) -> Optional[ConfigurationItem]:
        """获取生效的配置（应用优先级规则）"""
        if key not in self.configurations:
            return None
        
        configs = self.configurations[key]
        if not configs:
            return None
        
        # 按优先级排序，返回优先级最高的配置
        return min(configs, key=lambda config: ConfigurationPriority.get_priority(config.source))
    
    def get_all_configurations_for_key(self, key: str) -> List[ConfigurationItem]:
        """获取指定键的所有配置"""
        return self.configurations.get(key, [])
    
    def get_configurations_by_source(self, source: ConfigurationSource) -> Dict[str, ConfigurationItem]:
        """获取指定来源的所有配置"""
        result = {}
        for key, configs in self.configurations.items():
            for config in configs:
                if config.source == source:
                    result[key] = config
                    break
        return result
    
    def remove_configuration(self, key: str, source: ConfigurationSource = None) -> bool:
        """移除配置
        
        Args:
            key: 配置键
            source: 配置来源，如果为None则移除所有来源的配置
        
        Returns:
            是否成功移除
        """
        if key not in self.configurations:
            return False
        
        if source is None:
            # 移除所有来源的配置
            del self.configurations[key]
            self._update_conflict_reports(key)
            return True
        else:
            # 移除指定来源的配置
            original_count = len(self.configurations[key])
            self.configurations[key] = [c for c in self.configurations[key] if c.source != source]
            
            if len(self.configurations[key]) == 0:
                del self.configurations[key]
            
            if len(self.configurations[key]) != original_count:
                self._update_conflict_reports(key)
                return True
        
        return False
    
    def resolve_configuration_conflicts(self) -> Dict[str, ConfigurationItem]:
        """解析配置冲突，返回所有生效的配置"""
        resolved_configs = {}
        
        for key in self.configurations:
            effective_config = self.get_effective_configuration(key)
            if effective_config:
                resolved_configs[key] = effective_config
        
        self.logger.info(f"✅ 解析了 {len(resolved_configs)} 个配置项的冲突")
        return resolved_configs
    
    def detect_conflicts(self) -> List[ConflictReport]:
        """检测配置冲突"""
        self.conflict_reports = []
        
        for key, configs in self.configurations.items():
            if len(configs) > 1:
                # 存在多个配置，检查是否有冲突
                unique_values = set()
                for config in configs:
                    if isinstance(config.value, dict):
                        # 对于字典类型，转换为可哈希的形式
                        unique_values.add(json.dumps(config.value, sort_keys=True))
                    else:
                        unique_values.add(str(config.value))
                
                if len(unique_values) > 1:
                    # 存在不同的值，产生冲突
                    conflict_report = ConflictReport(key, configs)
                    self.conflict_reports.append(conflict_report)
        
        self.logger.info(f"🔍 检测到 {len(self.conflict_reports)} 个配置冲突")
        return self.conflict_reports
    
    def get_conflict_summary(self) -> str:
        """获取冲突摘要"""
        conflicts = self.detect_conflicts()
        if not conflicts:
            return "✅ 无配置冲突"
        
        summary_lines = [f"⚠️ 检测到 {len(conflicts)} 个配置冲突："]
        for conflict in conflicts:
            summary_lines.append(f"  • {conflict.get_conflict_summary()}")
        
        return "\n".join(summary_lines)
    
    def save_configuration_with_source(self, configurations: Dict[str, Any], 
                                     source: ConfigurationSource, 
                                     description: str = "") -> bool:
        """批量保存配置并记录来源"""
        try:
            for key, value in configurations.items():
                self.add_configuration(key, value, source, description)
            
            # 持久化到文件
            self._save_configurations_to_file()
            
            self.logger.info(f"💾 保存了 {len(configurations)} 个配置项 (来源: {source.value})")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 保存配置失败: {e}")
            return False
    
    def add_sheet_configuration(self, sheet_config: SheetConfiguration):
        """添加Sheet配置"""
        self.sheet_configurations[sheet_config.sheet_name] = sheet_config
        self.logger.debug(f"📊 添加Sheet配置: {sheet_config.sheet_name}")
    
    def get_sheet_configuration(self, sheet_name: str) -> Optional[SheetConfiguration]:
        """获取Sheet配置"""
        return self.sheet_configurations.get(sheet_name)
    
    def get_all_sheet_configurations(self) -> Dict[str, SheetConfiguration]:
        """获取所有Sheet配置"""
        return self.sheet_configurations.copy()
    
    def export_configuration(self, file_path: str, source: ConfigurationSource = None) -> bool:
        """导出配置到文件"""
        try:
            if source:
                # 导出指定来源的配置
                configs = self.get_configurations_by_source(source)
                export_data = {
                    "source": source.value,
                    "timestamp": datetime.now().isoformat(),
                    "configurations": {key: config.to_dict() for key, config in configs.items()}
                }
            else:
                # 导出所有有效配置
                resolved_configs = self.resolve_configuration_conflicts()
                export_data = {
                    "source": "RESOLVED",
                    "timestamp": datetime.now().isoformat(),
                    "configurations": {key: config.to_dict() for key, config in resolved_configs.items()}
                }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"📤 配置导出成功: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 配置导出失败: {e}")
            return False
    
    def import_configuration(self, file_path: str, source: ConfigurationSource = None) -> bool:
        """从文件导入配置"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                import_data = json.load(f)
            
            configurations = import_data.get('configurations', {})
            import_source = source or ConfigurationSource(import_data.get('source', 'USER_CONFIG'))
            
            for key, config_data in configurations.items():
                if isinstance(config_data, dict) and 'value' in config_data:
                    # 完整的配置项数据
                    config_item = ConfigurationItem.from_dict(config_data)
                    config_item.source = import_source  # 使用指定的来源
                    
                    if key not in self.configurations:
                        self.configurations[key] = []
                    self.configurations[key].append(config_item)
                else:
                    # 简单的键值对
                    self.add_configuration(key, config_data, import_source)
            
            self._save_configurations_to_file()
            self.logger.info(f"📥 配置导入成功: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 配置导入失败: {e}")
            return False
    
    def reset_configurations(self, source: ConfigurationSource = None):
        """重置配置"""
        if source is None:
            # 重置所有配置
            self.configurations.clear()
            self.sheet_configurations.clear()
            self.conflict_reports.clear()
            self.logger.info("🔄 重置所有配置")
        else:
            # 重置指定来源的配置
            keys_to_update = []
            for key, configs in self.configurations.items():
                original_count = len(configs)
                self.configurations[key] = [c for c in configs if c.source != source]
                if len(self.configurations[key]) != original_count:
                    keys_to_update.append(key)
            
            # 清理空的配置键
            for key in list(self.configurations.keys()):
                if not self.configurations[key]:
                    del self.configurations[key]
            
            # 更新冲突报告
            for key in keys_to_update:
                self._update_conflict_reports(key)
            
            self.logger.info(f"🔄 重置 {source.value} 来源的配置")
    
    def get_configuration_statistics(self) -> Dict[str, Any]:
        """获取配置统计信息"""
        stats = {
            "total_keys": len(self.configurations),
            "total_configurations": sum(len(configs) for configs in self.configurations.values()),
            "conflicts": len(self.conflict_reports),
            "by_source": {}
        }
        
        # 按来源统计
        for source in ConfigurationSource:
            configs = self.get_configurations_by_source(source)
            stats["by_source"][source.value] = len(configs)
        
        return stats
    
    def _update_conflict_reports(self, key: str):
        """更新指定键的冲突报告"""
        # 移除旧的冲突报告
        self.conflict_reports = [r for r in self.conflict_reports if r.field_key != key]
        
        # 检查是否需要添加新的冲突报告
        if key in self.configurations and len(self.configurations[key]) > 1:
            # 检查是否真的有冲突（值不同）
            configs = self.configurations[key]
            unique_values = set()
            for config in configs:
                if isinstance(config.value, dict):
                    unique_values.add(json.dumps(config.value, sort_keys=True))
                else:
                    unique_values.add(str(config.value))
            
            if len(unique_values) > 1:
                conflict_report = ConflictReport(key, configs)
                self.conflict_reports.append(conflict_report)
    
    def _load_configurations(self):
        """加载现有配置"""
        # 加载系统默认配置
        self._load_configuration_file(self.system_config_file, ConfigurationSource.SYSTEM_DEFAULT)
        
        # 加载模板配置
        self._load_configuration_file(self.template_config_file, ConfigurationSource.TABLE_TEMPLATE)
        
        # 加载用户配置
        self._load_configuration_file(self.user_config_file, ConfigurationSource.USER_CONFIG)
    
    def _load_configuration_file(self, file_path: Path, default_source: ConfigurationSource):
        """加载配置文件"""
        if not file_path.exists():
            return
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            configurations = data.get('configurations', {})
            for key, config_data in configurations.items():
                if isinstance(config_data, dict) and 'value' in config_data:
                    # 完整的配置项数据
                    try:
                        config_item = ConfigurationItem.from_dict(config_data)
                    except:
                        # 如果解析失败，使用默认来源
                        config_item = ConfigurationItem(
                            key=key,
                            value=config_data.get('value'),
                            source=default_source,
                            timestamp=datetime.now(),
                            description=config_data.get('description', '')
                        )
                else:
                    # 简单的键值对
                    config_item = ConfigurationItem(
                        key=key,
                        value=config_data,
                        source=default_source,
                        timestamp=datetime.now()
                    )
                
                if key not in self.configurations:
                    self.configurations[key] = []
                self.configurations[key].append(config_item)
            
            self.logger.debug(f"📁 加载配置文件: {file_path} ({len(configurations)} 项)")
            
        except Exception as e:
            self.logger.error(f"❌ 加载配置文件失败 {file_path}: {e}")
    
    def _save_configurations_to_file(self):
        """保存配置到文件"""
        # 按来源分别保存
        source_files = {
            ConfigurationSource.SYSTEM_DEFAULT: self.system_config_file,
            ConfigurationSource.TABLE_TEMPLATE: self.template_config_file,
            ConfigurationSource.USER_CONFIG: self.user_config_file
        }
        
        for source, file_path in source_files.items():
            configs = self.get_configurations_by_source(source)
            if configs:
                try:
                    export_data = {
                        "source": source.value,
                        "timestamp": datetime.now().isoformat(),
                        "configurations": {key: config.to_dict() for key, config in configs.items()}
                    }
                    
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump(export_data, f, ensure_ascii=False, indent=2)
                    
                    self.logger.debug(f"💾 保存配置文件: {file_path}")
                    
                except Exception as e:
                    self.logger.error(f"❌ 保存配置文件失败 {file_path}: {e}")


if __name__ == "__main__":
    """测试配置管理器"""
    import logging
    logging.basicConfig(level=logging.DEBUG)
    
    # 创建配置管理器
    manager = ConfigurationManager()
    
    # 添加一些测试配置
    manager.add_configuration("employee_id", "员工编号", ConfigurationSource.SYSTEM_DEFAULT, "系统默认映射")
    manager.add_configuration("employee_id", "人员编号", ConfigurationSource.USER_CONFIG, "用户自定义映射")
    manager.add_configuration("salary_base", "基本工资", ConfigurationSource.TABLE_TEMPLATE, "模板配置")
    
    # 检测冲突
    conflicts = manager.detect_conflicts()
    print("\n冲突检测结果:")
    for conflict in conflicts:
        print(f"  {conflict.get_conflict_summary()}")
    
    # 获取生效配置
    print("\n生效配置:")
    resolved = manager.resolve_configuration_conflicts()
    for key, config in resolved.items():
        print(f"  {key}: {config.value} (来源: {config.source.value})")
    
    # 配置统计
    print("\n配置统计:")
    stats = manager.get_configuration_statistics()
    print(json.dumps(stats, indent=2, ensure_ascii=False))
