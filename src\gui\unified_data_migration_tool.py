#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一配置界面数据迁移工具

负责将现有系统的配置数据迁移到新的统一配置系统中，包括：
- 原有异动表字段配置数据的迁移
- 原有Sheet映射配置数据的迁移
- 用户偏好设置的迁移
- 历史配置模板的迁移

创建时间: 2025-01-20
方案: 统一配置界面详细设计（方案3）- 阶段2实施
"""

import json
import os
import sys
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGroupBox, QLabel, 
    QPushButton, QTextEdit, QProgressBar, QMessageBox,
    QCheckBox, QListWidget, QListWidgetItem, QWidget,
    QTableWidget, QTableWidgetItem, QHeaderView
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QColor

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from src.utils.log_config import setup_logger
from src.gui.unified_config_manager import (
    ConfigurationManager, ConfigurationSource, ConfigurationItem
)


class MigrationTask:
    """迁移任务数据结构"""
    
    def __init__(self, task_id: str, name: str, description: str, 
                 source_path: str, enabled: bool = True):
        self.task_id = task_id
        self.name = name
        self.description = description
        self.source_path = source_path
        self.enabled = enabled
        self.status = "pending"  # pending, running, completed, failed
        self.progress = 0
        self.result_data = None
        self.error_message = None


class DataMigrationWorker(QThread):
    """数据迁移工作线程"""
    
    progress_updated = pyqtSignal(str, int)  # task_id, progress
    task_completed = pyqtSignal(str, bool, str)  # task_id, success, message
    migration_completed = pyqtSignal(bool, str)  # success, summary
    
    def __init__(self, tasks: List[MigrationTask], config_manager: ConfigurationManager):
        super().__init__()
        self.tasks = tasks
        self.config_manager = config_manager
        self.logger = setup_logger(__name__)
        self.should_stop = False
    
    def run(self):
        """执行迁移任务"""
        try:
            total_tasks = len([task for task in self.tasks if task.enabled])
            completed_tasks = 0
            failed_tasks = 0
            
            self.logger.info(f"🚀 开始数据迁移，共 {total_tasks} 个任务")
            
            for task in self.tasks:
                if self.should_stop:
                    break
                
                if not task.enabled:
                    continue
                
                self.logger.info(f"📦 开始迁移任务: {task.name}")
                task.status = "running"
                self.progress_updated.emit(task.task_id, 0)
                
                try:
                    # 根据任务类型执行不同的迁移逻辑
                    success = self._execute_migration_task(task)
                    
                    if success:
                        task.status = "completed"
                        completed_tasks += 1
                        self.task_completed.emit(task.task_id, True, "迁移成功")
                        self.logger.info(f"✅ 任务完成: {task.name}")
                    else:
                        task.status = "failed"
                        failed_tasks += 1
                        self.task_completed.emit(task.task_id, False, task.error_message or "迁移失败")
                        self.logger.error(f"❌ 任务失败: {task.name}")
                
                except Exception as e:
                    task.status = "failed"
                    task.error_message = str(e)
                    failed_tasks += 1
                    self.task_completed.emit(task.task_id, False, str(e))
                    self.logger.error(f"❌ 任务异常: {task.name} - {e}")
                
                self.progress_updated.emit(task.task_id, 100)
            
            # 迁移完成总结
            summary = f"迁移完成！成功: {completed_tasks}, 失败: {failed_tasks}"
            success = failed_tasks == 0
            self.migration_completed.emit(success, summary)
            
        except Exception as e:
            self.logger.error(f"❌ 迁移过程异常: {e}")
            self.migration_completed.emit(False, f"迁移过程异常: {e}")
    
    def _execute_migration_task(self, task: MigrationTask) -> bool:
        """执行具体的迁移任务"""
        try:
            if task.task_id == "change_data_configs":
                return self._migrate_change_data_configs(task)
            elif task.task_id == "sheet_mappings":
                return self._migrate_sheet_mappings(task)
            elif task.task_id == "user_preferences":
                return self._migrate_user_preferences(task)
            elif task.task_id == "field_types":
                return self._migrate_field_types(task)
            elif task.task_id == "templates":
                return self._migrate_templates(task)
            else:
                task.error_message = f"未知的迁移任务类型: {task.task_id}"
                return False
                
        except Exception as e:
            task.error_message = str(e)
            return False
    
    def _migrate_change_data_configs(self, task: MigrationTask) -> bool:
        """迁移异动表字段配置数据"""
        try:
            config_files = [
                "state/change_data_configs/change_data_config.json",
                "state/data/change_data_mapping.json"
            ]
            
            migrated_count = 0
            for config_file in config_files:
                if os.path.exists(config_file):
                    with open(config_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    # 转换为新的配置格式
                    for sheet_name, config in data.items():
                        if isinstance(config, dict):
                            # 提取字段映射
                            field_mapping = config.get('field_mapping', {})
                            field_types = config.get('field_types', {})
                            format_rules = config.get('formatting_rules', {})
                            
                            # 构建新的配置数据
                            new_config_data = {}
                            for excel_field, system_field in field_mapping.items():
                                new_config_data[f"{sheet_name}.{excel_field}"] = {
                                    'system_field': system_field,
                                    'field_type': field_types.get(excel_field, 'name_string'),
                                    'format_rules': format_rules.get(excel_field, {}),
                                    'source_sheet': sheet_name,
                                    'migration_source': 'change_data_config'
                                }
                            
                            # 保存到配置管理器
                            if new_config_data:
                                self.config_manager.save_configuration_with_source(
                                    new_config_data,
                                    ConfigurationSource.USER_CONFIG,
                                    f"从异动表配置迁移 - {sheet_name}"
                                )
                                migrated_count += len(new_config_data)
            
            task.result_data = {'migrated_count': migrated_count}
            self.logger.info(f"📦 迁移异动表配置: {migrated_count} 项")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 迁移异动表配置失败: {e}")
            task.error_message = str(e)
            return False
    
    def _migrate_sheet_mappings(self, task: MigrationTask) -> bool:
        """迁移Sheet映射配置数据"""
        try:
            config_files = [
                "state/multi_sheet_configs/multi_sheet_config.json",
                "state/data/sheet_mappings.json"
            ]
            
            migrated_count = 0
            for config_file in config_files:
                if os.path.exists(config_file):
                    with open(config_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    # 转换Sheet映射配置
                    if isinstance(data, dict):
                        for sheet_name, sheet_config in data.items():
                            if isinstance(sheet_config, dict):
                                new_config_data = {
                                    f"sheet.{sheet_name}.enabled": sheet_config.get('enabled', True),
                                    f"sheet.{sheet_name}.data_type": sheet_config.get('data_type', 'basic_info'),
                                    f"sheet.{sheet_name}.mappings": sheet_config.get('mappings', {})
                                }
                                
                                self.config_manager.save_configuration_with_source(
                                    new_config_data,
                                    ConfigurationSource.TABLE_TEMPLATE,
                                    f"从Sheet映射迁移 - {sheet_name}"
                                )
                                migrated_count += len(new_config_data)
            
            task.result_data = {'migrated_count': migrated_count}
            self.logger.info(f"📊 迁移Sheet映射: {migrated_count} 项")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 迁移Sheet映射失败: {e}")
            task.error_message = str(e)
            return False
    
    def _migrate_user_preferences(self, task: MigrationTask) -> bool:
        """迁移用户偏好设置"""
        try:
            preference_files = [
                "user_preferences.json",
                "state/user/preferences.json"
            ]
            
            migrated_count = 0
            for pref_file in preference_files:
                if os.path.exists(pref_file):
                    with open(pref_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    # 转换用户偏好
                    new_prefs = {}
                    if 'interface_mode' in data:
                        new_prefs['preferred_interface'] = data['interface_mode']
                    if 'column_widths' in data:
                        new_prefs['ui.column_widths'] = data['column_widths']
                    if 'recent_files' in data:
                        new_prefs['ui.recent_files'] = data['recent_files']
                    
                    if new_prefs:
                        self.config_manager.save_configuration_with_source(
                            new_prefs,
                            ConfigurationSource.USER_CONFIG,
                            "从用户偏好设置迁移"
                        )
                        migrated_count += len(new_prefs)
            
            task.result_data = {'migrated_count': migrated_count}
            self.logger.info(f"👤 迁移用户偏好: {migrated_count} 项")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 迁移用户偏好失败: {e}")
            task.error_message = str(e)
            return False
    
    def _migrate_field_types(self, task: MigrationTask) -> bool:
        """迁移字段类型定义"""
        try:
            # 迁移字段类型配置
            field_type_configs = {
                'salary_float': {
                    'data_type': 'float',
                    'format_rules': {'decimal_places': 2, 'thousands_separator': True},
                    'validation_rules': {'min_value': 0}
                },
                'name_string': {
                    'data_type': 'string',
                    'format_rules': {'trim_whitespace': True},
                    'validation_rules': {'not_empty': True, 'max_length': 50}
                },
                'employee_id': {
                    'data_type': 'string',
                    'format_rules': {'uppercase': True, 'trim_whitespace': True},
                    'validation_rules': {'not_empty': True, 'unique': True}
                },
                'date_field': {
                    'data_type': 'date',
                    'format_rules': {'date_format': 'YYYY-MM-DD'},
                    'validation_rules': {'valid_date': True}
                }
            }
            
            self.config_manager.save_configuration_with_source(
                field_type_configs,
                ConfigurationSource.SYSTEM_DEFAULT,
                "系统字段类型定义"
            )
            
            task.result_data = {'migrated_count': len(field_type_configs)}
            self.logger.info(f"🏷️ 迁移字段类型: {len(field_type_configs)} 项")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 迁移字段类型失败: {e}")
            task.error_message = str(e)
            return False
    
    def _migrate_templates(self, task: MigrationTask) -> bool:
        """迁移配置模板"""
        try:
            template_dirs = [
                "template/configs",
                "state/templates"
            ]
            
            migrated_count = 0
            for template_dir in template_dirs:
                if os.path.exists(template_dir):
                    for file_name in os.listdir(template_dir):
                        if file_name.endswith('.json'):
                            file_path = os.path.join(template_dir, file_name)
                            try:
                                with open(file_path, 'r', encoding='utf-8') as f:
                                    template_data = json.load(f)
                                
                                template_name = file_name.replace('.json', '')
                                new_template_data = {
                                    f"template.{template_name}": template_data
                                }
                                
                                self.config_manager.save_configuration_with_source(
                                    new_template_data,
                                    ConfigurationSource.TABLE_TEMPLATE,
                                    f"从模板文件迁移 - {template_name}"
                                )
                                migrated_count += 1
                                
                            except Exception as e:
                                self.logger.warning(f"⚠️ 跳过无效模板文件 {file_path}: {e}")
            
            task.result_data = {'migrated_count': migrated_count}
            self.logger.info(f"📋 迁移配置模板: {migrated_count} 项")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 迁移配置模板失败: {e}")
            task.error_message = str(e)
            return False
    
    def stop(self):
        """停止迁移"""
        self.should_stop = True


class DataMigrationDialog(QDialog):
    """数据迁移对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        self.config_manager = ConfigurationManager()
        self.migration_worker = None
        
        # 迁移任务列表
        self.migration_tasks = self._create_migration_tasks()
        
        self._init_ui()
        self._connect_signals()
        
        self.logger.info("🔧 数据迁移对话框初始化完成")
    
    def _init_ui(self):
        """初始化界面"""
        self.setWindowTitle("数据迁移工具 - 统一配置界面")
        self.setMinimumSize(700, 600)
        self.setModal(True)
        
        layout = QVBoxLayout(self)
        
        # 标题和说明
        title_label = QLabel("🔄 数据迁移工具")
        title_label.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        desc_label = QLabel(
            "此工具将现有配置数据迁移到新的统一配置系统中。\n"
            "请选择要迁移的数据类型，然后点击开始迁移。"
        )
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setStyleSheet("color: #666; margin: 10px;")
        layout.addWidget(desc_label)
        
        # 迁移任务列表
        task_group = self._create_task_list_group()
        layout.addWidget(task_group)
        
        # 迁移进度
        progress_group = self._create_progress_group()
        layout.addWidget(progress_group)
        
        # 按钮区域
        button_layout = self._create_button_layout()
        layout.addLayout(button_layout)
    
    def _create_migration_tasks(self) -> List[MigrationTask]:
        """创建迁移任务列表"""
        tasks = [
            MigrationTask(
                "change_data_configs",
                "异动表字段配置",
                "迁移现有的异动表字段映射、类型配置和格式化规则",
                "state/change_data_configs/",
                True
            ),
            MigrationTask(
                "sheet_mappings",
                "Sheet映射配置",
                "迁移现有的Sheet启用状态、数据类型分类等配置",
                "state/multi_sheet_configs/",
                True
            ),
            MigrationTask(
                "user_preferences",
                "用户偏好设置",
                "迁移用户界面设置、最近使用文件等偏好配置",
                "user_preferences.json",
                True
            ),
            MigrationTask(
                "field_types",
                "字段类型定义",
                "迁移和初始化系统字段类型定义",
                "内置",
                True
            ),
            MigrationTask(
                "templates",
                "配置模板",
                "迁移现有的配置模板文件",
                "template/configs/",
                True
            )
        ]
        
        return tasks
    
    def _create_task_list_group(self) -> QGroupBox:
        """创建任务列表组"""
        group = QGroupBox("📋 迁移任务")
        layout = QVBoxLayout(group)
        
        # 任务表格
        self.task_table = QTableWidget()
        self.task_table.setColumnCount(5)
        self.task_table.setHorizontalHeaderLabels([
            "启用", "任务名称", "描述", "状态", "进度"
        ])
        
        # 设置表格属性
        header = self.task_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.Stretch)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        
        self.task_table.setAlternatingRowColors(True)
        self.task_table.setSelectionBehavior(QTableWidget.SelectRows)
        
        # 填充任务数据
        self._populate_task_table()
        
        layout.addWidget(self.task_table)
        
        # 操作按钮
        task_btn_layout = QHBoxLayout()
        
        self.select_all_btn = QPushButton("✅ 全选")
        self.select_all_btn.clicked.connect(self._select_all_tasks)
        
        self.deselect_all_btn = QPushButton("❌ 全不选")
        self.deselect_all_btn.clicked.connect(self._deselect_all_tasks)
        
        self.check_sources_btn = QPushButton("🔍 检查数据源")
        self.check_sources_btn.clicked.connect(self._check_data_sources)
        
        task_btn_layout.addWidget(self.select_all_btn)
        task_btn_layout.addWidget(self.deselect_all_btn)
        task_btn_layout.addWidget(self.check_sources_btn)
        task_btn_layout.addStretch()
        
        layout.addLayout(task_btn_layout)
        
        return group
    
    def _create_progress_group(self) -> QGroupBox:
        """创建进度组"""
        group = QGroupBox("📊 迁移进度")
        layout = QVBoxLayout(group)
        
        # 总体进度
        self.overall_progress = QProgressBar()
        self.overall_progress.setTextVisible(True)
        layout.addWidget(self.overall_progress)
        
        # 当前任务状态
        self.current_task_label = QLabel("就绪")
        self.current_task_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        layout.addWidget(self.current_task_label)
        
        # 详细日志
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setReadOnly(True)
        self.log_text.setPlaceholderText("迁移日志将在此显示...")
        layout.addWidget(self.log_text)
        
        return group
    
    def _create_button_layout(self) -> QHBoxLayout:
        """创建按钮布局"""
        layout = QHBoxLayout()
        
        self.start_migration_btn = QPushButton("🚀 开始迁移")
        self.start_migration_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        self.start_migration_btn.clicked.connect(self._start_migration)
        
        self.stop_migration_btn = QPushButton("⏹️ 停止迁移")
        self.stop_migration_btn.setEnabled(False)
        self.stop_migration_btn.clicked.connect(self._stop_migration)
        
        self.close_btn = QPushButton("❌ 关闭")
        self.close_btn.clicked.connect(self.close)
        
        layout.addWidget(self.start_migration_btn)
        layout.addWidget(self.stop_migration_btn)
        layout.addStretch()
        layout.addWidget(self.close_btn)
        
        return layout
    
    def _populate_task_table(self):
        """填充任务表格"""
        self.task_table.setRowCount(len(self.migration_tasks))
        
        for row, task in enumerate(self.migration_tasks):
            # 启用复选框
            enable_check = QCheckBox()
            enable_check.setChecked(task.enabled)
            enable_check.toggled.connect(lambda checked, t=task: setattr(t, 'enabled', checked))
            self.task_table.setCellWidget(row, 0, enable_check)
            
            # 任务名称
            name_item = QTableWidgetItem(task.name)
            name_item.setFlags(name_item.flags() & ~Qt.ItemIsEditable)
            self.task_table.setItem(row, 1, name_item)
            
            # 描述
            desc_item = QTableWidgetItem(task.description)
            desc_item.setFlags(desc_item.flags() & ~Qt.ItemIsEditable)
            self.task_table.setItem(row, 2, desc_item)
            
            # 状态
            status_item = QTableWidgetItem("就绪")
            status_item.setFlags(status_item.flags() & ~Qt.ItemIsEditable)
            self.task_table.setItem(row, 3, status_item)
            
            # 进度
            progress_item = QTableWidgetItem("0%")
            progress_item.setFlags(progress_item.flags() & ~Qt.ItemIsEditable)
            self.task_table.setItem(row, 4, progress_item)
    
    def _connect_signals(self):
        """连接信号"""
        pass
    
    # 事件处理方法（占位）
    def _select_all_tasks(self):
        """全选任务"""
        for row in range(self.task_table.rowCount()):
            widget = self.task_table.cellWidget(row, 0)
            if isinstance(widget, QCheckBox):
                widget.setChecked(True)
    
    def _deselect_all_tasks(self):
        """全不选任务"""
        for row in range(self.task_table.rowCount()):
            widget = self.task_table.cellWidget(row, 0)
            if isinstance(widget, QCheckBox):
                widget.setChecked(False)
    
    def _check_data_sources(self):
        """检查数据源"""
        # TODO: 实现数据源检查
        QMessageBox.information(self, "数据源检查", "数据源检查功能正在开发中")
    
    def _start_migration(self):
        """开始迁移"""
        # TODO: 实现迁移启动
        QMessageBox.information(self, "开始迁移", "迁移功能正在开发中")
    
    def _stop_migration(self):
        """停止迁移"""
        # TODO: 实现迁移停止
        pass


if __name__ == "__main__":
    """测试数据迁移工具"""
    from PyQt5.QtWidgets import QApplication
    
    app = QApplication([])
    
    dialog = DataMigrationDialog()
    dialog.show()
    
    app.exec_()
