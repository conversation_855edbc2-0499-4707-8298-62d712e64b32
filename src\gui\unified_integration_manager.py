#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一配置界面集成管理器

负责将新的统一配置界面集成到现有系统中，提供：
- 新旧界面切换功能
- 配置数据兼容性处理
- 向后兼容性保证
- 渐进式迁移支持

创建时间: 2025-01-20
方案: 统一配置界面详细设计（方案3）- 阶段1实施
"""

import sys
import os
import json
from typing import Dict, List, Optional, Any, Tuple
from enum import Enum
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGroupBox, QLabel, 
    QPushButton, QComboBox, QCheckBox, QMessageBox, QWidget,
    QApplication, QMainWindow
)
from PyQt5.QtCore import Qt, pyqtSignal, QSettings
from PyQt5.QtGui import QFont, QIcon

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from src.utils.log_config import setup_logger
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager

# 导入新的统一配置对话框
from src.gui.unified_import_config_dialog import UnifiedImportConfigDialog

# 🆕 [第四阶段] 导入我们全新开发的统一数据导入窗口
from src.gui.unified_data_import_window import UnifiedDataImportWindow

# 🚫 [第四阶段] 传统配置对话框已被移除
# 以下导入已被注释，传统对话框不再可用
# try:
#     from src.gui.main_dialogs import DataImportDialog
#     from src.gui.change_data_config_dialog import ChangeDataConfigDialog
#     LEGACY_DIALOGS_AVAILABLE = True
# except ImportError:
#     LEGACY_DIALOGS_AVAILABLE = False

# 强制设置传统对话框为不可用
LEGACY_DIALOGS_AVAILABLE = False


class InterfaceMode(Enum):
    """界面模式枚举"""
    UNIFIED = "UNIFIED"           # 统一配置界面（旧版）
    UNIFIED_V2 = "UNIFIED_V2"     # 🆕 [第四阶段] 新版统一数据导入窗口
    LEGACY_SEPARATE = "LEGACY_SEPARATE"  # 原有的分离式界面
    AUTO_DETECT = "AUTO_DETECT"   # 自动检测模式


class UserPreference(Enum):
    """用户偏好设置"""
    PREFER_UNIFIED = "PREFER_UNIFIED"      # 偏好统一界面（旧版）
    PREFER_UNIFIED_V2 = "PREFER_UNIFIED_V2"  # 🆕 [第四阶段] 偏好新版统一界面
    PREFER_LEGACY = "PREFER_LEGACY"        # 偏好传统界面
    ASK_EVERY_TIME = "ASK_EVERY_TIME"      # 每次询问
    AUTO_DECIDE = "AUTO_DECIDE"            # 自动决定


class IntegrationManager:
    """集成管理器"""
    
    def __init__(self):
        """初始化集成管理器"""
        self.logger = setup_logger(__name__)
        self.settings = QSettings("SalarySystem", "UnifiedConfig")
        
        # 加载用户偏好
        self.user_preference = self._load_user_preference()
        self.current_interface_mode = InterfaceMode.AUTO_DETECT
        
        # 统计信息
        self.usage_stats = {
            "unified_usage_count": 0,
            "unified_v2_usage_count": 0,  # 🆕 [第四阶段] 新版本统计
            "legacy_usage_count": 0,
            "switch_count": 0,
            "last_used_mode": None
        }
        
        self._load_usage_stats()
        self.logger.info("🔧 集成管理器初始化完成")
    
    def show_import_dialog(self, parent=None, dynamic_table_manager: Optional[DynamicTableManager] = None, 
                          target_path: str = "", force_mode: Optional[InterfaceMode] = None) -> Optional[QDialog]:
        """显示数据导入对话框
        
        Args:
            parent: 父窗口
            dynamic_table_manager: 动态表管理器
            target_path: 目标路径
            force_mode: 强制使用的界面模式
            
        Returns:
            创建的对话框实例，如果用户取消则返回None
        """
        try:
            # 确定使用的界面模式
            if force_mode:
                interface_mode = force_mode
            else:
                interface_mode = self._determine_interface_mode(parent)
            
            if interface_mode is None:
                # 用户取消了选择
                return None
            
            # 根据模式创建相应的对话框
            dialog = self._create_dialog(interface_mode, parent, dynamic_table_manager, target_path)
            
            if dialog:
                # 更新使用统计
                self._update_usage_stats(interface_mode)
                
                # 显示对话框
                dialog.show()
                
                self.logger.info(f"📱 显示 {interface_mode.value} 模式的导入对话框")
            
            return dialog
            
        except Exception as e:
            self.logger.error(f"❌ 创建导入对话框失败: {e}")
            QMessageBox.critical(parent, "错误", f"创建导入对话框失败: {e}")
            return None
    
    def _determine_interface_mode(self, parent=None) -> Optional[InterfaceMode]:
        """确定使用的界面模式"""
        # 根据用户偏好确定
        if self.user_preference == UserPreference.PREFER_UNIFIED:
            return InterfaceMode.UNIFIED
        elif self.user_preference == UserPreference.PREFER_UNIFIED_V2:
            return InterfaceMode.UNIFIED_V2  # 🆕 [第四阶段] 使用新版统一界面
        elif self.user_preference == UserPreference.PREFER_LEGACY:
            return InterfaceMode.LEGACY_SEPARATE
        elif self.user_preference == UserPreference.ASK_EVERY_TIME:
            return self._show_interface_selection_dialog(parent)
        else:  # AUTO_DECIDE
            return self._auto_decide_interface_mode()
    
    def _show_interface_selection_dialog(self, parent=None) -> Optional[InterfaceMode]:
        """显示界面选择对话框"""
        dialog = InterfaceSelectionDialog(parent, self.usage_stats)
        result = dialog.exec_()
        
        if result == QDialog.Accepted:
            selected_mode = dialog.get_selected_mode()
            remember_choice = dialog.should_remember_choice()
            
            if remember_choice:
                # 更新用户偏好
                if selected_mode == InterfaceMode.UNIFIED:
                    self.user_preference = UserPreference.PREFER_UNIFIED
                else:
                    self.user_preference = UserPreference.PREFER_LEGACY
                self._save_user_preference()
            
            return selected_mode
        
        return None  # 用户取消
    
    def _auto_decide_interface_mode(self) -> InterfaceMode:
        """自动决定界面模式"""
        # 🆕 [第四阶段] 优先使用新版统一界面
        try:
            # 检查新版本是否可用
            from src.gui.unified_data_import_window import UnifiedDataImportWindow
            
            # 基于使用统计和其他因素自动决定
            unified_count = self.usage_stats.get("unified_usage_count", 0)
            unified_v2_count = self.usage_stats.get("unified_v2_usage_count", 0)
            legacy_count = self.usage_stats.get("legacy_usage_count", 0)
            
            # 如果用户已经使用过新版，继续使用新版
            if unified_v2_count > 0:
                self.logger.info("🎯 基于使用历史选择新版统一界面")
                return InterfaceMode.UNIFIED_V2
            
            # 如果是新用户，默认使用新版统一界面
            if unified_count + legacy_count < 5:
                self.logger.info("🎯 新用户默认使用新版统一界面")
                return InterfaceMode.UNIFIED_V2
            
            # 如果用户更多使用统一界面，升级到新版
            if unified_count > legacy_count * 1.5:
                self.logger.info("🎯 基于统一界面使用习惯升级到新版")
                return InterfaceMode.UNIFIED_V2
            
            # 其他情况也尝试使用新版（推广新功能）
            self.logger.info("🎯 推广新版统一界面")
            return InterfaceMode.UNIFIED_V2
            
        except ImportError as e:
            self.logger.warning(f"新版统一界面不可用: {e}，回退到旧版")
            return InterfaceMode.UNIFIED
        except Exception as e:
            self.logger.warning(f"检查新版界面时出错: {e}，使用传统界面")
            return InterfaceMode.LEGACY_SEPARATE
    
    def _create_dialog(self, interface_mode: InterfaceMode, parent, 
                      dynamic_table_manager, target_path) -> Optional[QDialog]:
        """根据模式创建对话框"""
        if interface_mode == InterfaceMode.UNIFIED:
            return self._create_unified_dialog(parent, dynamic_table_manager)
        elif interface_mode == InterfaceMode.UNIFIED_V2:
            return self._create_unified_v2_dialog(parent, dynamic_table_manager)  # 🆕 [第四阶段] 新版统一界面
        elif interface_mode == InterfaceMode.LEGACY_SEPARATE:
            return self._create_legacy_dialog(parent, dynamic_table_manager, target_path)
        else:
            raise ValueError(f"不支持的界面模式: {interface_mode}")
    
    def _create_unified_dialog(self, parent, dynamic_table_manager) -> UnifiedImportConfigDialog:
        """创建统一配置对话框（旧版）"""
        dialog = UnifiedImportConfigDialog(parent, dynamic_table_manager)
        
        # 连接信号
        dialog.data_imported.connect(self._on_unified_data_imported)
        dialog.config_applied.connect(self._on_unified_config_applied)
        
        return dialog
    
    def _create_unified_v2_dialog(self, parent, dynamic_table_manager) -> UnifiedDataImportWindow:
        """🆕 [第四阶段] 创建新版统一数据导入窗口"""
        try:
            dialog = UnifiedDataImportWindow(parent)
            
            # 设置动态表管理器（如果新窗口支持的话）
            if hasattr(dialog, 'set_dynamic_table_manager'):
                dialog.set_dynamic_table_manager(dynamic_table_manager)
            elif hasattr(dialog, 'import_manager') and hasattr(dialog.import_manager, 'table_manager'):
                dialog.import_manager.table_manager = dynamic_table_manager
            
            # 连接信号兼容性处理
            # 新窗口的信号: import_completed, status_updated, progress_updated
            # 需要适配到旧接口: data_imported, config_applied
            
            # 适配导入完成信号
            dialog.import_completed.connect(self._on_unified_v2_import_completed)
            
            # 适配状态更新（可以作为配置应用的指示）
            dialog.status_updated.connect(self._on_unified_v2_status_updated)
            
            self.logger.info("🎯 成功创建新版统一数据导入窗口")
            return dialog
            
        except Exception as e:
            self.logger.error(f"❌ 创建新版统一界面失败: {e}")
            # 回退到旧版本
            self.logger.info("🔄 回退到旧版统一界面")
            return self._create_unified_dialog(parent, dynamic_table_manager)
    
    def _create_legacy_dialog(self, parent, dynamic_table_manager, target_path) -> Optional[QDialog]:
        """🚫 传统配置对话框已被移除 - 重定向到新版统一界面"""
        self.logger.warning("⚠️ 传统对话框已被完全移除，强制使用新版统一界面")
        
        # 强制重定向到新版统一界面
        return self._create_unified_v2_dialog(parent, dynamic_table_manager)
        
        # 🗑️ 以下代码已被移除：
        # - LEGACY_DIALOGS_AVAILABLE 检查
        # - DataImportDialog 创建
        # - 传统信号连接
        # 
        # 所有传统导入功能已迁移到新版统一界面
    
    def _load_user_preference(self) -> UserPreference:
        """加载用户偏好设置"""
        preference_str = self.settings.value("user_preference", UserPreference.AUTO_DECIDE.value)
        try:
            return UserPreference(preference_str)
        except ValueError:
            return UserPreference.AUTO_DECIDE
    
    def _save_user_preference(self):
        """保存用户偏好设置"""
        self.settings.setValue("user_preference", self.user_preference.value)
        self.logger.debug(f"💾 保存用户偏好: {self.user_preference.value}")
    
    def _load_usage_stats(self):
        """加载使用统计"""
        stats_str = self.settings.value("usage_stats", "{}")
        try:
            saved_stats = json.loads(stats_str)
            self.usage_stats.update(saved_stats)
        except (json.JSONDecodeError, TypeError):
            pass  # 使用默认统计信息
    
    def _save_usage_stats(self):
        """保存使用统计"""
        stats_str = json.dumps(self.usage_stats)
        self.settings.setValue("usage_stats", stats_str)
    
    def _update_usage_stats(self, interface_mode: InterfaceMode):
        """更新使用统计"""
        if interface_mode == InterfaceMode.UNIFIED:
            self.usage_stats["unified_usage_count"] = self.usage_stats.get("unified_usage_count", 0) + 1
        elif interface_mode == InterfaceMode.UNIFIED_V2:
            self.usage_stats["unified_v2_usage_count"] = self.usage_stats.get("unified_v2_usage_count", 0) + 1  # 🆕 [第四阶段] 新版本统计
        elif interface_mode == InterfaceMode.LEGACY_SEPARATE:
            self.usage_stats["legacy_usage_count"] = self.usage_stats.get("legacy_usage_count", 0) + 1
        
        # 检查是否是模式切换
        if (self.usage_stats.get("last_used_mode") and 
            self.usage_stats["last_used_mode"] != interface_mode.value):
            self.usage_stats["switch_count"] = self.usage_stats.get("switch_count", 0) + 1
        
        self.usage_stats["last_used_mode"] = interface_mode.value
        self._save_usage_stats()
    
    def _on_unified_data_imported(self, data: dict):
        """统一界面数据导入完成事件"""
        self.logger.info("✅ 统一界面数据导入完成")
        # 可以在这里添加额外的处理逻辑
    
    def _on_unified_config_applied(self, config: dict):
        """统一界面配置应用事件"""
        self.logger.info("🔧 统一界面配置应用完成")
        # 可以在这里添加额外的处理逻辑
    
    def _on_legacy_data_imported(self, data: dict):
        """传统界面数据导入完成事件"""
        self.logger.info("✅ 传统界面数据导入完成")
        # 可以在这里添加额外的处理逻辑
    
    def _on_unified_v2_import_completed(self, success: bool, message: str):
        """🆕 [第四阶段] 新版统一界面导入完成事件"""
        if success:
            self.logger.info("🎯 新版统一界面数据导入成功")
            # 模拟旧接口的data_imported信号
            self._emit_compatible_data_imported_signal({"success": True, "message": message})
        else:
            self.logger.error(f"❌ 新版统一界面数据导入失败: {message}")
    
    def _on_unified_v2_status_updated(self, status: str):
        """🆕 [第四阶段] 新版统一界面状态更新事件"""
        self.logger.debug(f"📊 新版统一界面状态: {status}")
        # 可以根据状态判断是否有配置变化
        if "配置" in status or "映射" in status:
            # 模拟旧接口的config_applied信号
            self._emit_compatible_config_applied_signal({"status": status})
    
    def _emit_compatible_data_imported_signal(self, data: dict):
        """发送兼容的数据导入信号"""
        # 这里可以发送兼容的信号，让上层调用者能够接收到
        # 或者直接记录日志
        self.logger.info("🔗 发送兼容数据导入信号")
    
    def _emit_compatible_config_applied_signal(self, config: dict):
        """发送兼容的配置应用信号"""
        # 这里可以发送兼容的信号，让上层调用者能够接收到
        # 或者直接记录日志
        self.logger.info("🔗 发送兼容配置应用信号")
    
    def get_usage_report(self) -> str:
        """获取使用情况报告"""
        unified_count = self.usage_stats["unified_usage_count"]
        unified_v2_count = self.usage_stats.get("unified_v2_usage_count", 0)  # 🆕 [第四阶段]
        legacy_count = self.usage_stats["legacy_usage_count"]
        total_usage = unified_count + unified_v2_count + legacy_count
        
        if total_usage == 0:
            return "暂无使用记录"
        
        unified_rate = unified_count / total_usage * 100
        unified_v2_rate = unified_v2_count / total_usage * 100  # 🆕 [第四阶段]
        legacy_rate = legacy_count / total_usage * 100
        
        report = f"""
📊 界面使用统计报告
{'='*30}
统一界面(旧版)使用次数: {unified_count} ({unified_rate:.1f}%)
🆕 统一界面(新版)使用次数: {unified_v2_count} ({unified_v2_rate:.1f}%)
传统界面使用次数: {legacy_count} ({legacy_rate:.1f}%)
界面切换次数: {self.usage_stats['switch_count']}
最后使用模式: {self.usage_stats.get('last_used_mode', '未知')}
当前用户偏好: {self.user_preference.value}
"""
        
        return report.strip()


class InterfaceSelectionDialog(QDialog):
    """界面选择对话框"""
    
    def __init__(self, parent=None, usage_stats: Dict[str, Any] = None):
        super().__init__(parent)
        self.usage_stats = usage_stats or {}
        self.selected_mode = InterfaceMode.UNIFIED
        self.remember_choice = False
        
        self._init_ui()
    
    def _init_ui(self):
        """初始化界面"""
        self.setWindowTitle("选择配置界面")
        self.setMinimumSize(500, 350)
        self.setModal(True)
        
        layout = QVBoxLayout(self)
        
        # 标题和说明
        title_label = QLabel("🎯 选择数据导入配置界面")
        title_label.setFont(QFont("Microsoft YaHei", 14, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        desc_label = QLabel("系统提供两种配置界面，请选择您偏好的方式：")
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setStyleSheet("color: #666; margin: 10px;")
        layout.addWidget(desc_label)
        
        # 统一界面选项
        unified_group = self._create_unified_option_group()
        layout.addWidget(unified_group)
        
        # 传统界面选项
        legacy_group = self._create_legacy_option_group()
        layout.addWidget(legacy_group)
        
        # 使用统计信息
        if self.usage_stats:
            stats_group = self._create_stats_group()
            layout.addWidget(stats_group)
        
        # 记住选择选项
        self.remember_check = QCheckBox("记住我的选择，下次不再询问")
        layout.addWidget(self.remember_check)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.unified_btn = QPushButton("✅ 使用统一界面")
        self.unified_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        self.unified_btn.clicked.connect(self._select_unified)
        
        self.legacy_btn = QPushButton("📋 使用传统界面")
        self.legacy_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        self.legacy_btn.clicked.connect(self._select_legacy)
        
        cancel_btn = QPushButton("❌ 取消")
        cancel_btn.clicked.connect(self.reject)
        
        button_layout.addWidget(self.unified_btn)
        button_layout.addWidget(self.legacy_btn)
        button_layout.addStretch()
        button_layout.addWidget(cancel_btn)
        
        layout.addLayout(button_layout)
    
    def _create_unified_option_group(self) -> QGroupBox:
        """创建统一界面选项组"""
        group = QGroupBox("🎨 统一配置界面 (推荐)")
        layout = QVBoxLayout(group)
        
        features = [
            "✅ 一个界面完成所有配置",
            "🎯 智能配置冲突检测和解决",
            "🎨 可视化配置来源指示", 
            "🤖 智能自动映射功能",
            "📊 实时预览和验证",
            "⚡ 更好的用户体验"
        ]
        
        for feature in features:
            label = QLabel(feature)
            label.setStyleSheet("color: #27ae60; margin: 2px;")
            layout.addWidget(label)
        
        return group
    
    def _create_legacy_option_group(self) -> QGroupBox:
        """创建传统界面选项组"""
        group = QGroupBox("📋 传统分离式界面")
        layout = QVBoxLayout(group)
        
        features = [
            "📁 熟悉的界面布局",
            "🔧 独立的配置对话框",
            "📊 分步式配置流程",
            "🛠️ 与现有工作流程兼容"
        ]
        
        for feature in features:
            label = QLabel(feature)
            label.setStyleSheet("color: #3498db; margin: 2px;")
            layout.addWidget(label)
        
        return group
    
    def _create_stats_group(self) -> QGroupBox:
        """创建统计信息组"""
        group = QGroupBox("📈 您的使用情况")
        layout = QVBoxLayout(group)
        
        unified_count = self.usage_stats.get("unified_usage_count", 0)
        legacy_count = self.usage_stats.get("legacy_usage_count", 0)
        total_count = unified_count + legacy_count
        
        if total_count > 0:
            unified_rate = unified_count / total_count * 100
            legacy_rate = legacy_count / total_count * 100
            
            stats_text = f"""
            统一界面使用: {unified_count} 次 ({unified_rate:.1f}%)
            传统界面使用: {legacy_count} 次 ({legacy_rate:.1f}%)
            """
            
            stats_label = QLabel(stats_text.strip())
            stats_label.setStyleSheet("color: #666; font-family: monospace;")
            layout.addWidget(stats_label)
            
            # 推荐
            if unified_count > legacy_count:
                recommendation = "💡 基于您的使用习惯，推荐继续使用统一界面"
            elif legacy_count > unified_count:
                recommendation = "💡 基于您的使用习惯，推荐继续使用传统界面"
            else:
                recommendation = "💡 建议尝试新的统一界面，体验更好的功能"
            
            rec_label = QLabel(recommendation)
            rec_label.setStyleSheet("color: #f39c12; font-style: italic;")
            layout.addWidget(rec_label)
        else:
            new_user_label = QLabel("🎉 欢迎使用！推荐您试试新的统一配置界面")
            new_user_label.setStyleSheet("color: #27ae60; font-weight: bold;")
            layout.addWidget(new_user_label)
        
        return group
    
    def _select_unified(self):
        """选择统一界面"""
        self.selected_mode = InterfaceMode.UNIFIED
        self.remember_choice = self.remember_check.isChecked()
        self.accept()
    
    def _select_legacy(self):
        """选择传统界面"""
        self.selected_mode = InterfaceMode.LEGACY_SEPARATE
        self.remember_choice = self.remember_check.isChecked()
        self.accept()
    
    def get_selected_mode(self) -> InterfaceMode:
        """获取选择的模式"""
        return self.selected_mode
    
    def should_remember_choice(self) -> bool:
        """是否记住选择"""
        return self.remember_choice


# 全局集成管理器实例
_integration_manager = None

def get_integration_manager() -> IntegrationManager:
    """获取全局集成管理器实例"""
    global _integration_manager
    if _integration_manager is None:
        _integration_manager = IntegrationManager()
    return _integration_manager


def show_unified_import_dialog(parent=None, dynamic_table_manager: Optional[DynamicTableManager] = None, 
                              target_path: str = "") -> Optional[QDialog]:
    """显示统一数据导入对话框（便捷函数）
    
    这是主要的入口点，外部代码应该使用这个函数来显示导入对话框。
    """
    manager = get_integration_manager()
    return manager.show_import_dialog(parent, dynamic_table_manager, target_path)


if __name__ == "__main__":
    """测试集成管理器"""
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = QMainWindow()
    window.setWindowTitle("集成管理器测试")
    window.resize(300, 200)
    
    # 添加测试按钮
    central_widget = QWidget()
    layout = QVBoxLayout(central_widget)
    
    test_btn = QPushButton("测试显示导入对话框")
    test_btn.clicked.connect(lambda: show_unified_import_dialog(window))
    layout.addWidget(test_btn)
    
    report_btn = QPushButton("显示使用报告")
    def show_report():
        manager = get_integration_manager()
        report = manager.get_usage_report()
        QMessageBox.information(window, "使用报告", report)
    report_btn.clicked.connect(show_report)
    layout.addWidget(report_btn)
    
    window.setCentralWidget(central_widget)
    window.show()
    
    sys.exit(app.exec_())
