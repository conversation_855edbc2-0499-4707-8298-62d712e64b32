2025-08-30 20:18:12.210 | INFO     | src.utils.log_config:_log_initialization_info:289 | 日志系统初始化完成
2025-08-30 20:18:12.210 | INFO     | src.utils.log_config:_log_initialization_info:290 | 日志级别: INFO
2025-08-30 20:18:12.210 | INFO     | src.utils.log_config:_log_initialization_info:291 | 控制台输出: True
2025-08-30 20:18:12.210 | INFO     | src.utils.log_config:_log_initialization_info:292 | 文件输出: True
2025-08-30 20:18:12.210 | INFO     | src.utils.log_config:_log_initialization_info:298 | 日志文件路径: logs/salary_system.log
2025-08-30 20:18:12.214 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-08-30 20:18:15.539 | INFO     | src.gui.style_manager:__init__:68 | StyleManager初始化完成
2025-08-30 20:18:15.539 | INFO     | src.gui.style_manager:apply_global_style:93 | 开始应用全局Material Design样式...
2025-08-30 20:18:15.539 | INFO     | src.gui.style_manager:apply_global_style:105 | 全局样式应用成功
2025-08-30 20:18:15.539 | INFO     | src.gui.style_manager:enable_hot_reload:416 | 样式热重载未启用（非开发环境）
2025-08-30 20:18:15.539 | INFO     | __main__:setup_app_logging:423 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-08-30 20:18:15.539 | INFO     | __main__:main:487 | 初始化核心管理器...
2025-08-30 20:18:15.539 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-30 20:18:15.539 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-30 20:18:15.539 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-30 20:18:15.539 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-30 20:18:15.555 | INFO     | src.modules.data_storage.database_manager:_initialize_database:102 | 开始初始化数据库...
2025-08-30 20:18:15.568 | INFO     | src.modules.data_storage.database_manager:_initialize_database:157 | 数据库初始化完成
2025-08-30 20:18:15.568 | INFO     | src.modules.data_storage.database_manager:__init__:98 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-08-30 20:18:15.568 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-30 20:18:15.579 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:107 | 动态表管理器初始化完成
2025-08-30 20:18:15.579 | INFO     | __main__:main:492 | 核心管理器初始化完成。
2025-08-30 20:18:15.582 | INFO     | src.core.data_source_unification_manager:__init__:128 | 数据源统一管理器初始化完成
2025-08-30 20:18:15.583 | INFO     | src.core.table_sort_state_manager:__init__:177 | 表级排序状态管理器初始化完成
2025-08-30 20:18:15.583 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-30 20:18:15.584 | INFO     | src.core.error_handler_manager:__init__:111 | 🔧 [P2-3] 错误处理管理器初始化完成
2025-08-30 20:18:15.594 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: data_error_medium
2025-08-30 20:18:15.595 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: ui_error_medium
2025-08-30 20:18:15.596 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: system_error_medium
2025-08-30 20:18:15.597 | INFO     | src.gui.prototype.prototype_main_window:_register_recovery_strategies:11931 | 🔧 [P2-3] 错误恢复策略注册完成
2025-08-30 20:18:15.598 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-30 20:18:15.599 | INFO     | src.gui.prototype.prototype_main_window:_setup_error_handling:11786 | 🔧 [P2-3] 错误处理机制设置完成
2025-08-30 20:18:15.605 | INFO     | src.gui.prototype.prototype_main_window:_init_recursive_call_protection:11824 | 🔧 [P1-2] 递归调用防护机制初始化完成
2025-08-30 20:18:15.816 | INFO     | src.core.architecture_factory:__init__:62 | 架构重构工厂初始化完成
2025-08-30 20:18:15.816 | INFO     | src.core.architecture_factory:initialize_architecture:72 | 开始初始化架构重构系统...
2025-08-30 20:18:15.816 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-30 20:18:15.823 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:153 | 使用已存在的配置文件: state/data/field_mappings.json
2025-08-30 20:18:15.823 | INFO     | src.modules.data_import.config_sync_manager:__init__:81 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-08-30 20:18:15.823 | INFO     | src.core.unified_state_manager:_load_state:585 | 状态已从文件加载: state/unified_state.json
2025-08-30 20:18:15.826 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-30 20:18:15.826 | INFO     | src.core.field_mapping_manager:_load_config:89 | 🔧 [P3优化] 字段映射配置加载成功
2025-08-30 20:18:15.826 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:153 | 使用已存在的配置文件: state/data/field_mappings.json
2025-08-30 20:18:15.826 | INFO     | src.modules.data_import.config_sync_manager:__init__:81 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-08-30 20:18:15.826 | INFO     | src.modules.format_management.field_registry:__init__:95 | 🏷️ [字段注册] 字段注册系统初始化: state/data/field_mappings.json
2025-08-30 20:18:15.826 | INFO     | src.core.unified_mapping_service:__init__:45 | UnifiedMappingService 初始化完成
2025-08-30 20:18:15.826 | INFO     | src.core.unified_data_request_manager:__init__:217 | 统一数据请求管理器初始化完成
2025-08-30 20:18:15.826 | INFO     | src.core.unified_data_request_manager:__init__:217 | 统一数据请求管理器初始化完成
2025-08-30 20:18:15.826 | INFO     | src.core.unified_state_manager:_load_state:585 | 状态已从文件加载: state/unified_state.json
2025-08-30 20:18:15.826 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-30 20:18:15.826 | INFO     | src.services.table_data_service:__init__:84 | 表格数据服务初始化完成
2025-08-30 20:18:15.826 | INFO     | src.core.architecture_factory:initialize_architecture:103 | 🎉 架构重构系统初始化成功！耗时: 10.3ms
2025-08-30 20:18:15.859 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:46 | 数据预加载缓存初始化完成 - 最大条目数: 100, TTL: 300秒
2025-08-30 20:18:15.859 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:232 | 表格状态缓存初始化完成
2025-08-30 20:18:15.864 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:32 | 表头配置缓存初始化完成
2025-08-30 20:18:15.865 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:211 | 字段映射缓存初始化完成
2025-08-30 20:18:15.867 | INFO     | src.gui.prototype.performance.performance_manager:__init__:46 | 🚀 性能管理器初始化完成
2025-08-30 20:18:15.868 | INFO     | src.gui.prototype.prototype_main_window:__init__:3651 | 🚀 性能管理器已集成
2025-08-30 20:18:15.869 | INFO     | src.gui.prototype.prototype_main_window:__init__:3653 | ✅ 新架构集成成功！
2025-08-30 20:18:15.870 | INFO     | src.gui.prototype.prototype_main_window:_inject_config_sync_manager_to_existing_tables:3766 | ConfigSyncManager重新注入完成，已更新0个表格实例
2025-08-30 20:18:15.871 | INFO     | src.gui.prototype.prototype_main_window:_setup_new_architecture_listeners:3731 | ✅ 新架构事件监听器设置完成
2025-08-30 20:18:15.871 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-08-30 20:18:15.873 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-08-30 20:18:15.883 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-08-30 20:18:16.410 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:2727 | 菜单栏创建完成
2025-08-30 20:18:16.414 | INFO     | src.gui.prototype.prototype_main_window:__init__:2702 | 菜单栏管理器初始化完成
2025-08-30 20:18:16.415 | INFO     | src.gui.table_header_manager:__init__:105 | 🔧 [P1-3] 性能优化表头管理器初始化完成
2025-08-30 20:18:16.415 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:5459 | 管理器设置完成，包含增强版表头管理器
2025-08-30 20:18:16.415 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5464 | 🔧 开始应用窗口级Material Design样式...
2025-08-30 20:18:16.418 | INFO     | src.gui.style_manager:apply_window_style:123 | 开始应用窗口级样式...
2025-08-30 20:18:16.422 | INFO     | src.gui.style_manager:apply_window_style:136 | 窗口级样式应用成功
2025-08-30 20:18:16.422 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5471 | ✅ 窗口级样式应用成功
2025-08-30 20:18:16.423 | INFO     | src.gui.prototype.prototype_main_window:_setup_responsive_style:5512 | ✅ 响应式样式监听设置完成
2025-08-30 20:18:16.423 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-30 20:18:16.423 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-30 20:18:16.423 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-30 20:18:16.423 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:165 | 导航状态加载成功
2025-08-30 20:18:16.423 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-08-30 20:18:16.433 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-08-30 20:18:16.433 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-30 20:18:16.433 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1992 | 元数据项 salary_data_2025_05 缺少年份或月份信息，已跳过
2025-08-30 20:18:16.449 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_fallback_salary_data:1835 | 🏠 使用示例数据结构加载导航（首次启动或暂无数据时显示）
2025-08-30 20:18:16.466 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 4 个匹配类型 'change_data' 的表 (尝试 1/5)
2025-08-30 20:18:16.466 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-30 20:18:16.466 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:924 | 恢复导航状态: 4个展开项
2025-08-30 20:18:16.466 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > 全部在职人员', '工资表', '工资表 > 2025年', '工资表 > 2025年 > 05月']
2025-08-30 20:18:16.472 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-08-30 20:18:16.477 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:165 | 导航状态加载成功
2025-08-30 20:18:16.477 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-08-30 20:18:16.480 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-08-30 20:18:16.490 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-30 20:18:16.490 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1992 | 元数据项 salary_data_2025_05 缺少年份或月份信息，已跳过
2025-08-30 20:18:16.494 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 4 个匹配类型 'change_data' 的表 (尝试 1/5)
2025-08-30 20:18:16.497 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-30 20:18:16.503 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['异动人员表 > 2025年 > 12月 > A岗职工', '异动人员表 > 2025年 > 12月 > 离休人员工资表', '异动人员表 > 2025年 > 12月', '异动人员表 > 2025年 > 12月 > 退休人员工资表', '异动人员表 > 2025年']
2025-08-30 20:18:16.505 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['异动人员表 > 2025年 > 12月 > A岗职工', '异动人员表 > 2025年 > 12月 > 离休人员工资表', '异动人员表 > 2025年 > 12月', '异动人员表 > 2025年 > 12月 > 退休人员工资表', '异动人员表 > 2025年']
2025-08-30 20:18:16.506 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-08-30 20:18:16.507 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1396 | 🔧 [P1-2修复] 开始自动选择最新数据...
2025-08-30 20:18:16.507 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-30 20:18:16.510 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-30 20:18:16.512 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2173 | 未找到有效的工资数据表
2025-08-30 20:18:16.545 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1601 | 🔧 [P1-2修复] 检查表状态失败，1.0s后重试...
2025-08-30 20:18:16.546 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1405 | 🔧 [P1-2修复] 未找到最新工资数据路径（可能是首次启动）
2025-08-30 20:18:16.811 | INFO     | src.gui.prototype.widgets.header_update_manager:__init__:68 | HeaderUpdateManager 初始化完成，关联表格: VirtualizedExpandableTable
2025-08-30 20:18:16.812 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2142 | 🚨 [架构修复] 启用模型数据同步机制
2025-08-30 20:18:16.817 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1366 | 快捷键注册完成: 18/18 个
2025-08-30 20:18:16.817 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1809 | 拖拽排序管理器初始化完成
2025-08-30 20:18:16.845 | INFO     | src.modules.data_management.data_flow_validator:__init__:85 | 🔧 [数据验证器] 初始化完成，验证级别: lenient
2025-08-30 20:18:16.846 | INFO     | src.modules.state_management.table_state_manager:__init__:103 | 🔧 [状态管理] 仅使用内存缓存
2025-08-30 20:18:16.848 | INFO     | src.modules.state_management.table_state_manager:__init__:105 | 🔧 [状态管理] 初始化完成，最大缓存: 100
2025-08-30 20:18:16.863 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2195 | 🔧 [P0-紧急修复] 数据流验证器初始化成功（宽松模式）
2025-08-30 20:18:16.865 | INFO     | src.modules.data_import.header_edit_manager:__init__:82 | 表头编辑管理器初始化完成
2025-08-30 20:18:16.867 | INFO     | src.core.unified_state_manager:_load_state:585 | 状态已从文件加载: state/unified_state.json
2025-08-30 20:18:16.868 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-30 20:18:16.869 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2247 | 🎯 [统一格式管理] 使用统一格式管理器
2025-08-30 20:18:16.882 | INFO     | src.gui.prototype.widgets.column_sort_manager:_load_field_mappings_from_config:370 | 🔧 [新架构] 成功加载 46 个字段映射
2025-08-30 20:18:16.883 | INFO     | src.gui.prototype.widgets.column_sort_manager:__init__:102 | 🆕 [新架构排序] 多列排序管理器初始化完成，最大排序列数: 3
2025-08-30 20:18:16.884 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2294 | 🆕 [新架构多列排序] 初始化完成，支持最多3列排序
2025-08-30 20:18:16.889 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1553 | 🔧 [列宽保存修复] 列宽管理器初始化完成
2025-08-30 20:18:16.889 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1554 | 🔧 [列宽保存修复] 配置文件绝对路径: C:\test\salary_changes\salary_changes\state\column_widths.json
2025-08-30 20:18:16.890 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1555 | 🔧 [列宽保存修复] 配置文件存在: True
2025-08-30 20:18:16.891 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1556 | 🔧 [列宽保存修复] 父目录存在: True
2025-08-30 20:18:16.891 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1557 | 🔧 [列宽保存修复] 当前工作目录: C:\test\salary_changes\salary_changes
2025-08-30 20:18:16.892 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2301 | 列宽管理器初始化完成
2025-08-30 20:18:16.893 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:setup_ui:2428 | 🔧 [架构修复] 保持自定义排序功能，修复数据显示问题
2025-08-30 20:18:16.895 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2315 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-08-30 20:18:16.895 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2307 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-30 20:18:16.896 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-30 20:18:16.904 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-30 20:18:16.915 | INFO     | src.gui.prototype.widgets.pagination_state_manager:__init__:78 | 🔧 [P1优化] 分页状态管理器初始化完成
2025-08-30 20:18:16.917 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7339 | 方案A：安全设置列数: 0
2025-08-30 20:18:16.918 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2838 | 🔧 [P1优化] 表切换操作: None -> default_table
2025-08-30 20:18:16.918 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-30 20:18:16.926 | INFO     | src.gui.prototype.widgets.table_header_cache:__init__:67 | 🔧 [P1优化] 表头缓存管理器初始化完成，最大缓存: 100
2025-08-30 20:18:16.930 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2890 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-30 20:18:16.930 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-08-30 20:18:16.946 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-30 20:18:16.947 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-30 20:18:16.948 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-30 20:18:16.949 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-30 20:18:16.950 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:443 | 表格数据已设置: 0 行, 22 列
2025-08-30 20:18:16.954 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:443 | 🔧 [新架构] 排序管理器切换到表格: default_table
2025-08-30 20:18:16.956 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 49.9ms
2025-08-30 20:18:16.962 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-30 20:18:16.963 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-30 20:18:16.968 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-30 20:18:16.970 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-30 20:18:16.983 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2332 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-30 20:18:16.991 | INFO     | src.gui.widgets.pagination_widget:_apply_styles:298 | 分页组件Material Design样式应用成功
2025-08-30 20:18:17.017 | INFO     | src.gui.widgets.pagination_widget:__init__:176 | ✅ [防抖升级] 智能防抖系统已启用
2025-08-30 20:18:17.018 | INFO     | src.gui.widgets.pagination_widget:__init__:184 | 分页组件初始化完成
2025-08-30 20:18:17.069 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:595 | 控制面板按钮信号连接完成
2025-08-30 20:18:17.118 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:5421 | 快捷键设置完成
2025-08-30 20:18:17.119 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:5378 | 主窗口UI设置完成。
2025-08-30 20:18:17.122 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5615 | 🔧 [全局排序] 全局排序开关连接成功
2025-08-30 20:18:17.123 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5647 | 🆕 [新架构排序] 使用表格组件内部的自定义排序循环，无需连接排序信号
2025-08-30 20:18:17.123 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5659 | ✅ 已连接分页刷新信号到主窗口
2025-08-30 20:18:17.124 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5660 | ✅ 已连接分页组件事件到新架构
2025-08-30 20:18:17.126 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5671 | ✅ 已连接概览标签页刷新按钮到主窗口
2025-08-30 20:18:17.127 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5674 | 信号连接设置完成
2025-08-30 20:18:17.128 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6963 | 🔧 [P1-2修复] 发现 6 个表的配置
2025-08-30 20:18:17.129 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:488 | 🔧 [P1-1修复] 加载统一格式映射: salary_data_2025_07_active_employees, 10个字段
2025-08-30 20:18:17.130 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:488 | 🔧 [P1-1修复] 加载统一格式映射: active_employees, 5个字段
2025-08-30 20:18:17.136 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: change_data_2025_12_全部在职人员工资表, 30个字段
2025-08-30 20:18:17.137 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: change_data_2025_12_退休人员工资表, 30个字段
2025-08-30 20:18:17.138 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: change_data_2025_12_A岗职工, 30个字段
2025-08-30 20:18:17.139 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: change_data_2025_12_离休人员工资表, 30个字段
2025-08-30 20:18:17.140 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6973 | ✅ [P1-2修复] 已加载字段映射信息，共6个表的映射
2025-08-30 20:18:17.148 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2307 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-30 20:18:17.149 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-30 20:18:17.150 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-30 20:18:17.150 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-30 20:18:17.151 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-08-30 20:18:17.152 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7339 | 方案A：安全设置列数: 22
2025-08-30 20:18:17.155 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-30 20:18:17.156 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-30 20:18:17.161 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-30 20:18:17.162 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-30 20:18:17.163 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 13.0ms
2025-08-30 20:18:17.164 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-30 20:18:17.165 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-30 20:18:17.167 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-30 20:18:17.167 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-30 20:18:17.168 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2332 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-30 20:18:17.169 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-30 20:18:17.169 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8665 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-08-30 20:18:17.176 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2307 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-30 20:18:17.177 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-30 20:18:17.178 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-30 20:18:17.179 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-30 20:18:17.179 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-08-30 20:18:17.180 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-30 20:18:17.181 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-30 20:18:17.184 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-30 20:18:17.193 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-30 20:18:17.201 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 23.4ms
2025-08-30 20:18:17.201 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-30 20:18:17.203 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-30 20:18:17.205 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-30 20:18:17.206 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-30 20:18:17.206 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2332 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-30 20:18:17.207 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8683 | 已显示标准空表格，表头数量: 22
2025-08-30 20:18:17.208 | INFO     | src.gui.prototype.prototype_main_window:__init__:3705 | 原型主窗口初始化完成
2025-08-30 20:18:17.415 | INFO     | __main__:main:514 | 应用程序启动成功
2025-08-30 20:18:17.422 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-30 20:18:17.423 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-30 20:18:17.424 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1474 | 执行延迟的自动选择最新数据...
2025-08-30 20:18:17.425 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-30 20:18:17.566 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1280px)
2025-08-30 20:18:17.567 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2080 | MainWorkspaceArea 响应式适配: sm
2025-08-30 20:18:17.573 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-30 20:18:17.578 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-30 20:18:17.581 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2173 | 未找到有效的工资数据表
2025-08-30 20:18:18.075 | INFO     | src.gui.prototype.prototype_main_window:delayed_heavy_fixes:9567 | UI亮度问题检测到并修复: ['table_opacity_in_stylesheet']
2025-08-30 20:18:18.075 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9477 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-08-30 20:18:18.080 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9491 | 🔧 [P0-1] 检测到亮度问题: ['table_opacity_in_stylesheet']
2025-08-30 20:18:18.080 | INFO     | src.gui.prototype.prototype_main_window:_apply_brightness_fixes:10025 | 🔧 [P1-2] 开始应用增强版亮度修复: ['table_opacity_in_stylesheet']
2025-08-30 20:18:18.100 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9497 | 🔧 [P0-1] 智能显示亮度修复完成
2025-08-30 20:18:18.583 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1396 | 🔧 [P1-2修复] 开始自动选择最新数据...
2025-08-30 20:18:18.583 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-30 20:18:18.583 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-30 20:18:18.583 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2173 | 未找到有效的工资数据表
2025-08-30 20:18:19.583 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-30 20:18:19.586 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-30 20:18:19.588 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2173 | 未找到有效的工资数据表
2025-08-30 20:18:19.588 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1612 | 🔧 [P1-2修复] 3次重试已用尽，可能数据导入尚未完成
2025-08-30 20:18:27.998 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:655 | 数据导入功能被触发，发出 import_requested 信号。
2025-08-30 20:18:27.998 | INFO     | src.gui.prototype.prototype_main_window:_get_suggested_target_path:8488 | 检测到当前在工资表TAB，生成工资表默认路径
2025-08-30 20:18:27.998 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:5907 | 接收到数据导入请求，推断的目标路径: 工资表 > 2025年 > 08月 > 全部在职人员。打开导入对话框。
2025-08-30 20:18:27.998 | INFO     | src.gui.prototype.prototype_main_window:_should_use_unified_interface:5924 | 🎯 直接使用新版统一数据导入界面
2025-08-30 20:18:28.097 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-30 20:18:28.097 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-30 20:18:28.097 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-30 20:18:28.097 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-30 20:18:28.097 | INFO     | src.modules.data_storage.database_manager:_initialize_database:102 | 开始初始化数据库...
2025-08-30 20:18:28.097 | INFO     | src.modules.data_storage.database_manager:_initialize_database:157 | 数据库初始化完成
2025-08-30 20:18:28.108 | INFO     | src.modules.data_storage.database_manager:__init__:98 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-08-30 20:18:28.109 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-30 20:18:28.118 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:107 | 动态表管理器初始化完成
2025-08-30 20:18:28.118 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-08-30 20:18:28.122 | INFO     | src.core.config_cache_manager:__init__:67 | 配置缓存管理器初始化完成: cache\config_cache, 最大条目数: 100
2025-08-30 20:18:28.122 | INFO     | src.modules.data_import.change_data_config_manager:__init__:69 | 配置缓存已启用
2025-08-30 20:18:28.123 | INFO     | src.modules.data_import.change_data_config_manager:__init__:75 | 配置管理器初始化完成，配置目录: C:\test\salary_changes\salary_changes\state\change_data_configs
2025-08-30 20:18:28.123 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:78 | 多Sheet导入器初始化完成
2025-08-30 20:18:28.175 | INFO     | src.gui.unified_config_manager:__init__:193 | 🔧 配置管理器初始化完成
2025-08-30 20:19:16.665 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:178 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-08-30 20:19:16.671 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-08-30 20:19:16.673 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-08-30 20:19:16.673 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-30 20:19:16.773 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 16列 (列过滤: 否)
2025-08-30 20:19:16.773 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-30 20:19:16.773 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-08-30 20:19:16.773 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 3行 x 16列
2025-08-30 20:19:16.789 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:599 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-30 20:19:16.789 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:642 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-08-30 20:19:16.789 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 2行 × 16列
2025-08-30 20:19:16.789 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-30 20:19:16.889 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-30 20:19:16.889 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 27 列 (原始 27 列)
2025-08-30 20:19:16.905 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-30 20:19:17.009 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-30 20:19:17.009 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 23 列 (原始 23 列)
2025-08-30 20:19:17.015 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-30 20:19:17.115 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-30 20:19:17.123 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-08-30 20:19:20.939 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-08-30 20:19:20.939 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-08-30 20:19:20.957 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-30 20:19:21.055 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 16列 (列过滤: 否)
2025-08-30 20:19:21.059 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-30 20:19:21.059 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-08-30 20:19:21.059 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 1行 x 16列
2025-08-30 20:19:21.065 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 1行 × 16列
2025-08-30 20:19:40.524 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:178 | 检测到工作表: ['起薪', '转正定级', '停薪-退休', '停薪-调离', '停薪-其他', '停薪-调整', '其他-考核扣款', '脱产读博', '请假扣款']
2025-08-30 20:19:40.524 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 5月异动人员5.7.xlsx (0.03MB)
2025-08-30 20:19:40.524 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 5月异动人员5.7.xlsx
2025-08-30 20:19:40.524 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\异动人员\5月异动人员5.7.xlsx: read
2025-08-30 20:19:40.576 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 14列 (列过滤: 否)
2025-08-30 20:19:40.576 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-30 20:19:40.576 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 14 列 (原始 14 列)
2025-08-30 20:19:40.576 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 2行 x 14列
2025-08-30 20:19:40.576 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 2行 × 14列
2025-08-30 20:19:40.576 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\异动人员\5月异动人员5.7.xlsx: read
2025-08-30 20:19:40.626 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-30 20:19:40.626 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 12 列 (原始 12 列)
2025-08-30 20:19:40.642 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\异动人员\5月异动人员5.7.xlsx: read
2025-08-30 20:19:40.689 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-30 20:19:40.689 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 11 列 (原始 11 列)
2025-08-30 20:19:40.689 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\异动人员\5月异动人员5.7.xlsx: read
2025-08-30 20:19:40.738 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-30 20:19:40.738 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 12 列 (原始 12 列)
2025-08-30 20:19:40.738 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\异动人员\5月异动人员5.7.xlsx: read
2025-08-30 20:19:40.781 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-30 20:19:40.797 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 12 列 (原始 12 列)
2025-08-30 20:19:40.797 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\异动人员\5月异动人员5.7.xlsx: read
2025-08-30 20:19:40.855 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-30 20:19:40.859 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 10 列 (原始 10 列)
2025-08-30 20:19:40.863 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:628 | 过滤记录[行1]: 工号字段(工作证号)为空, 数据: {'姓名': '无'}
2025-08-30 20:19:40.865 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:632 | 数据导入过滤: 发现1条工号为空的记录
2025-08-30 20:19:40.865 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:642 | 数据过滤完成: 原始1条记录，过滤1条无效记录，有效记录0条
2025-08-30 20:19:40.865 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\异动人员\5月异动人员5.7.xlsx: read
2025-08-30 20:19:40.914 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-30 20:19:40.914 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 9 列 (原始 9 列)
2025-08-30 20:19:40.921 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\异动人员\5月异动人员5.7.xlsx: read
2025-08-30 20:19:40.965 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-30 20:19:40.965 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 9 列 (原始 9 列)
2025-08-30 20:19:40.973 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\异动人员\5月异动人员5.7.xlsx: read
2025-08-30 20:19:41.023 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-30 20:19:41.023 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 12 列 (原始 12 列)
2025-08-30 20:19:44.688 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 5月异动人员5.7.xlsx (0.03MB)
2025-08-30 20:19:44.688 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 5月异动人员5.7.xlsx
2025-08-30 20:19:44.688 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\异动人员\5月异动人员5.7.xlsx: read
2025-08-30 20:19:44.723 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 14列 (列过滤: 否)
2025-08-30 20:19:44.739 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-30 20:19:44.749 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 14 列 (原始 14 列)
2025-08-30 20:19:44.749 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 1行 x 14列
2025-08-30 20:19:44.749 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 1行 × 14列
2025-08-30 20:19:52.772 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:178 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-08-30 20:19:52.773 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-08-30 20:19:52.773 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-08-30 20:19:52.773 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-30 20:19:52.873 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 16列 (列过滤: 否)
2025-08-30 20:19:52.873 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-30 20:19:52.873 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-08-30 20:19:52.873 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 3行 x 16列
2025-08-30 20:19:52.889 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:599 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-30 20:19:52.889 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:642 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-08-30 20:19:52.889 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 2行 × 16列
2025-08-30 20:19:52.889 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-30 20:19:53.071 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-30 20:19:53.073 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 27 列 (原始 27 列)
2025-08-30 20:19:53.073 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-30 20:19:53.178 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-30 20:19:53.178 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 23 列 (原始 23 列)
2025-08-30 20:19:53.178 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-30 20:19:53.491 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-30 20:19:53.491 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-08-30 20:20:19.116 | INFO     | src.gui.prototype.prototype_main_window:_show_unified_import_dialog:6019 | 用户取消了数据导入
2025-08-30 20:20:20.884 | INFO     | __main__:main:519 | 应用程序正常退出
2025-08-30 20:35:35.586 | INFO     | src.utils.log_config:_log_initialization_info:289 | 日志系统初始化完成
2025-08-30 20:35:35.587 | INFO     | src.utils.log_config:_log_initialization_info:290 | 日志级别: INFO
2025-08-30 20:35:35.587 | INFO     | src.utils.log_config:_log_initialization_info:291 | 控制台输出: True
2025-08-30 20:35:35.587 | INFO     | src.utils.log_config:_log_initialization_info:292 | 文件输出: True
2025-08-30 20:35:35.587 | INFO     | src.utils.log_config:_log_initialization_info:298 | 日志文件路径: logs/salary_system.log
2025-08-30 20:35:35.588 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-08-30 20:35:37.192 | INFO     | src.gui.style_manager:__init__:68 | StyleManager初始化完成
2025-08-30 20:35:37.193 | INFO     | src.gui.style_manager:apply_global_style:93 | 开始应用全局Material Design样式...
2025-08-30 20:35:37.193 | INFO     | src.gui.style_manager:apply_global_style:105 | 全局样式应用成功
2025-08-30 20:35:37.193 | INFO     | src.gui.style_manager:enable_hot_reload:416 | 样式热重载未启用（非开发环境）
2025-08-30 20:35:37.193 | INFO     | __main__:setup_app_logging:423 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-08-30 20:35:37.193 | INFO     | __main__:main:487 | 初始化核心管理器...
2025-08-30 20:35:37.193 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-30 20:35:37.194 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-30 20:35:37.194 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-30 20:35:37.194 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-30 20:35:37.194 | INFO     | src.modules.data_storage.database_manager:_initialize_database:102 | 开始初始化数据库...
2025-08-30 20:35:37.216 | INFO     | src.modules.data_storage.database_manager:_initialize_database:157 | 数据库初始化完成
2025-08-30 20:35:37.218 | INFO     | src.modules.data_storage.database_manager:__init__:98 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-08-30 20:35:37.218 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-30 20:35:37.225 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:107 | 动态表管理器初始化完成
2025-08-30 20:35:37.225 | INFO     | __main__:main:492 | 核心管理器初始化完成。
2025-08-30 20:35:37.228 | INFO     | src.core.data_source_unification_manager:__init__:128 | 数据源统一管理器初始化完成
2025-08-30 20:35:37.228 | INFO     | src.core.table_sort_state_manager:__init__:177 | 表级排序状态管理器初始化完成
2025-08-30 20:35:37.229 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-30 20:35:37.229 | INFO     | src.core.error_handler_manager:__init__:111 | 🔧 [P2-3] 错误处理管理器初始化完成
2025-08-30 20:35:37.229 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: data_error_medium
2025-08-30 20:35:37.229 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: ui_error_medium
2025-08-30 20:35:37.229 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: system_error_medium
2025-08-30 20:35:37.229 | INFO     | src.gui.prototype.prototype_main_window:_register_recovery_strategies:11931 | 🔧 [P2-3] 错误恢复策略注册完成
2025-08-30 20:35:37.230 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-30 20:35:37.230 | INFO     | src.gui.prototype.prototype_main_window:_setup_error_handling:11786 | 🔧 [P2-3] 错误处理机制设置完成
2025-08-30 20:35:37.230 | INFO     | src.gui.prototype.prototype_main_window:_init_recursive_call_protection:11824 | 🔧 [P1-2] 递归调用防护机制初始化完成
2025-08-30 20:35:37.271 | INFO     | src.core.architecture_factory:__init__:62 | 架构重构工厂初始化完成
2025-08-30 20:35:37.271 | INFO     | src.core.architecture_factory:initialize_architecture:72 | 开始初始化架构重构系统...
2025-08-30 20:35:37.271 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-30 20:35:37.276 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:153 | 使用已存在的配置文件: state/data/field_mappings.json
2025-08-30 20:35:37.282 | INFO     | src.modules.data_import.config_sync_manager:__init__:81 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-08-30 20:35:37.283 | INFO     | src.core.unified_state_manager:_load_state:585 | 状态已从文件加载: state/unified_state.json
2025-08-30 20:35:37.283 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-30 20:35:37.284 | INFO     | src.core.field_mapping_manager:_load_config:89 | 🔧 [P3优化] 字段映射配置加载成功
2025-08-30 20:35:37.291 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:153 | 使用已存在的配置文件: state/data/field_mappings.json
2025-08-30 20:35:37.292 | INFO     | src.modules.data_import.config_sync_manager:__init__:81 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-08-30 20:35:37.293 | INFO     | src.modules.format_management.field_registry:__init__:95 | 🏷️ [字段注册] 字段注册系统初始化: state/data/field_mappings.json
2025-08-30 20:35:37.294 | INFO     | src.core.unified_mapping_service:__init__:45 | UnifiedMappingService 初始化完成
2025-08-30 20:35:37.294 | INFO     | src.core.unified_data_request_manager:__init__:217 | 统一数据请求管理器初始化完成
2025-08-30 20:35:37.296 | INFO     | src.core.unified_data_request_manager:__init__:217 | 统一数据请求管理器初始化完成
2025-08-30 20:35:37.297 | INFO     | src.core.unified_state_manager:_load_state:585 | 状态已从文件加载: state/unified_state.json
2025-08-30 20:35:37.297 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-30 20:35:37.298 | INFO     | src.services.table_data_service:__init__:84 | 表格数据服务初始化完成
2025-08-30 20:35:37.298 | INFO     | src.core.architecture_factory:initialize_architecture:103 | 🎉 架构重构系统初始化成功！耗时: 26.8ms
2025-08-30 20:35:37.300 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:46 | 数据预加载缓存初始化完成 - 最大条目数: 100, TTL: 300秒
2025-08-30 20:35:37.300 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:232 | 表格状态缓存初始化完成
2025-08-30 20:35:37.300 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:32 | 表头配置缓存初始化完成
2025-08-30 20:35:37.300 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:211 | 字段映射缓存初始化完成
2025-08-30 20:35:37.300 | INFO     | src.gui.prototype.performance.performance_manager:__init__:46 | 🚀 性能管理器初始化完成
2025-08-30 20:35:37.300 | INFO     | src.gui.prototype.prototype_main_window:__init__:3651 | 🚀 性能管理器已集成
2025-08-30 20:35:37.300 | INFO     | src.gui.prototype.prototype_main_window:__init__:3653 | ✅ 新架构集成成功！
2025-08-30 20:35:37.300 | INFO     | src.gui.prototype.prototype_main_window:_inject_config_sync_manager_to_existing_tables:3766 | ConfigSyncManager重新注入完成，已更新0个表格实例
2025-08-30 20:35:37.300 | INFO     | src.gui.prototype.prototype_main_window:_setup_new_architecture_listeners:3731 | ✅ 新架构事件监听器设置完成
2025-08-30 20:35:37.300 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-08-30 20:35:37.300 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-08-30 20:35:37.300 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-08-30 20:35:37.713 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:2727 | 菜单栏创建完成
2025-08-30 20:35:37.714 | INFO     | src.gui.prototype.prototype_main_window:__init__:2702 | 菜单栏管理器初始化完成
2025-08-30 20:35:37.714 | INFO     | src.gui.table_header_manager:__init__:105 | 🔧 [P1-3] 性能优化表头管理器初始化完成
2025-08-30 20:35:37.714 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:5459 | 管理器设置完成，包含增强版表头管理器
2025-08-30 20:35:37.714 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5464 | 🔧 开始应用窗口级Material Design样式...
2025-08-30 20:35:37.715 | INFO     | src.gui.style_manager:apply_window_style:123 | 开始应用窗口级样式...
2025-08-30 20:35:37.717 | INFO     | src.gui.style_manager:apply_window_style:136 | 窗口级样式应用成功
2025-08-30 20:35:37.717 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5471 | ✅ 窗口级样式应用成功
2025-08-30 20:35:37.717 | INFO     | src.gui.prototype.prototype_main_window:_setup_responsive_style:5512 | ✅ 响应式样式监听设置完成
2025-08-30 20:35:37.722 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-30 20:35:37.722 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-30 20:35:37.722 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-30 20:35:37.724 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:165 | 导航状态加载成功
2025-08-30 20:35:37.725 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-08-30 20:35:37.729 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-08-30 20:35:37.750 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-30 20:35:37.752 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1992 | 元数据项 salary_data_2025_05 缺少年份或月份信息，已跳过
2025-08-30 20:35:37.765 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_fallback_salary_data:1835 | 🏠 使用示例数据结构加载导航（首次启动或暂无数据时显示）
2025-08-30 20:35:37.769 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 4 个匹配类型 'change_data' 的表 (尝试 1/5)
2025-08-30 20:35:37.773 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-30 20:35:37.782 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:924 | 恢复导航状态: 4个展开项
2025-08-30 20:35:37.782 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > 全部在职人员', '工资表', '工资表 > 2025年', '工资表 > 2025年 > 05月']
2025-08-30 20:35:37.783 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-08-30 20:35:37.784 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:165 | 导航状态加载成功
2025-08-30 20:35:37.784 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-08-30 20:35:37.786 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-08-30 20:35:37.796 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-30 20:35:37.796 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1992 | 元数据项 salary_data_2025_05 缺少年份或月份信息，已跳过
2025-08-30 20:35:37.802 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 4 个匹配类型 'change_data' 的表 (尝试 1/5)
2025-08-30 20:35:37.804 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-30 20:35:37.822 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['异动人员表 > 2025年 > 12月 > A岗职工', '异动人员表 > 2025年 > 12月 > 离休人员工资表', '异动人员表 > 2025年 > 12月 > 退休人员工资表', '异动人员表 > 2025年 > 12月', '异动人员表 > 2025年']
2025-08-30 20:35:37.822 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['异动人员表 > 2025年 > 12月 > A岗职工', '异动人员表 > 2025年 > 12月 > 离休人员工资表', '异动人员表 > 2025年 > 12月 > 退休人员工资表', '异动人员表 > 2025年 > 12月', '异动人员表 > 2025年']
2025-08-30 20:35:37.822 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-08-30 20:35:37.822 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1396 | 🔧 [P1-2修复] 开始自动选择最新数据...
2025-08-30 20:35:37.822 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-30 20:35:37.828 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-30 20:35:37.828 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2173 | 未找到有效的工资数据表
2025-08-30 20:35:37.828 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1601 | 🔧 [P1-2修复] 检查表状态失败，1.0s后重试...
2025-08-30 20:35:37.838 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1405 | 🔧 [P1-2修复] 未找到最新工资数据路径（可能是首次启动）
2025-08-30 20:35:38.215 | INFO     | src.gui.prototype.widgets.header_update_manager:__init__:68 | HeaderUpdateManager 初始化完成，关联表格: VirtualizedExpandableTable
2025-08-30 20:35:38.215 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2142 | 🚨 [架构修复] 启用模型数据同步机制
2025-08-30 20:35:38.218 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1366 | 快捷键注册完成: 18/18 个
2025-08-30 20:35:38.218 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1809 | 拖拽排序管理器初始化完成
2025-08-30 20:35:38.229 | INFO     | src.modules.data_management.data_flow_validator:__init__:85 | 🔧 [数据验证器] 初始化完成，验证级别: lenient
2025-08-30 20:35:38.230 | INFO     | src.modules.state_management.table_state_manager:__init__:103 | 🔧 [状态管理] 仅使用内存缓存
2025-08-30 20:35:38.230 | INFO     | src.modules.state_management.table_state_manager:__init__:105 | 🔧 [状态管理] 初始化完成，最大缓存: 100
2025-08-30 20:35:38.230 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2195 | 🔧 [P0-紧急修复] 数据流验证器初始化成功（宽松模式）
2025-08-30 20:35:38.231 | INFO     | src.modules.data_import.header_edit_manager:__init__:82 | 表头编辑管理器初始化完成
2025-08-30 20:35:38.231 | INFO     | src.core.unified_state_manager:_load_state:585 | 状态已从文件加载: state/unified_state.json
2025-08-30 20:35:38.231 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-30 20:35:38.231 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2247 | 🎯 [统一格式管理] 使用统一格式管理器
2025-08-30 20:35:38.239 | INFO     | src.gui.prototype.widgets.column_sort_manager:_load_field_mappings_from_config:370 | 🔧 [新架构] 成功加载 46 个字段映射
2025-08-30 20:35:38.240 | INFO     | src.gui.prototype.widgets.column_sort_manager:__init__:102 | 🆕 [新架构排序] 多列排序管理器初始化完成，最大排序列数: 3
2025-08-30 20:35:38.241 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2294 | 🆕 [新架构多列排序] 初始化完成，支持最多3列排序
2025-08-30 20:35:38.243 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1553 | 🔧 [列宽保存修复] 列宽管理器初始化完成
2025-08-30 20:35:38.243 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1554 | 🔧 [列宽保存修复] 配置文件绝对路径: C:\test\salary_changes\salary_changes\state\column_widths.json
2025-08-30 20:35:38.244 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1555 | 🔧 [列宽保存修复] 配置文件存在: True
2025-08-30 20:35:38.244 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1556 | 🔧 [列宽保存修复] 父目录存在: True
2025-08-30 20:35:38.244 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1557 | 🔧 [列宽保存修复] 当前工作目录: C:\test\salary_changes\salary_changes
2025-08-30 20:35:38.245 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2301 | 列宽管理器初始化完成
2025-08-30 20:35:38.274 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:setup_ui:2428 | 🔧 [架构修复] 保持自定义排序功能，修复数据显示问题
2025-08-30 20:35:38.278 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2315 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-08-30 20:35:38.278 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2307 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-30 20:35:38.279 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-30 20:35:38.279 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-30 20:35:38.282 | INFO     | src.gui.prototype.widgets.pagination_state_manager:__init__:78 | 🔧 [P1优化] 分页状态管理器初始化完成
2025-08-30 20:35:38.283 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7339 | 方案A：安全设置列数: 0
2025-08-30 20:35:38.284 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2838 | 🔧 [P1优化] 表切换操作: None -> default_table
2025-08-30 20:35:38.284 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-30 20:35:38.287 | INFO     | src.gui.prototype.widgets.table_header_cache:__init__:67 | 🔧 [P1优化] 表头缓存管理器初始化完成，最大缓存: 100
2025-08-30 20:35:38.288 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2890 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-30 20:35:38.288 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-08-30 20:35:38.302 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-30 20:35:38.302 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-30 20:35:38.303 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-30 20:35:38.303 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-30 20:35:38.303 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:443 | 表格数据已设置: 0 行, 22 列
2025-08-30 20:35:38.305 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:443 | 🔧 [新架构] 排序管理器切换到表格: default_table
2025-08-30 20:35:38.307 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 26.3ms
2025-08-30 20:35:38.309 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-30 20:35:38.309 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-30 20:35:38.312 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-30 20:35:38.313 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-30 20:35:38.313 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2332 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-30 20:35:38.327 | INFO     | src.gui.widgets.pagination_widget:_apply_styles:298 | 分页组件Material Design样式应用成功
2025-08-30 20:35:38.336 | INFO     | src.gui.widgets.pagination_widget:__init__:176 | ✅ [防抖升级] 智能防抖系统已启用
2025-08-30 20:35:38.336 | INFO     | src.gui.widgets.pagination_widget:__init__:184 | 分页组件初始化完成
2025-08-30 20:35:38.437 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:595 | 控制面板按钮信号连接完成
2025-08-30 20:35:38.719 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:5421 | 快捷键设置完成
2025-08-30 20:35:38.720 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:5378 | 主窗口UI设置完成。
2025-08-30 20:35:38.720 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5615 | 🔧 [全局排序] 全局排序开关连接成功
2025-08-30 20:35:38.720 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5647 | 🆕 [新架构排序] 使用表格组件内部的自定义排序循环，无需连接排序信号
2025-08-30 20:35:38.720 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5659 | ✅ 已连接分页刷新信号到主窗口
2025-08-30 20:35:38.720 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5660 | ✅ 已连接分页组件事件到新架构
2025-08-30 20:35:38.721 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5671 | ✅ 已连接概览标签页刷新按钮到主窗口
2025-08-30 20:35:38.721 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5674 | 信号连接设置完成
2025-08-30 20:35:38.723 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6963 | 🔧 [P1-2修复] 发现 6 个表的配置
2025-08-30 20:35:38.724 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:488 | 🔧 [P1-1修复] 加载统一格式映射: salary_data_2025_07_active_employees, 10个字段
2025-08-30 20:35:38.725 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:488 | 🔧 [P1-1修复] 加载统一格式映射: active_employees, 5个字段
2025-08-30 20:35:38.725 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: change_data_2025_12_全部在职人员工资表, 30个字段
2025-08-30 20:35:38.726 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: change_data_2025_12_退休人员工资表, 30个字段
2025-08-30 20:35:38.727 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: change_data_2025_12_A岗职工, 30个字段
2025-08-30 20:35:38.728 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: change_data_2025_12_离休人员工资表, 30个字段
2025-08-30 20:35:38.728 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6973 | ✅ [P1-2修复] 已加载字段映射信息，共6个表的映射
2025-08-30 20:35:38.736 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2307 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-30 20:35:38.737 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-30 20:35:38.737 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-30 20:35:38.737 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-30 20:35:38.737 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-08-30 20:35:38.738 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7339 | 方案A：安全设置列数: 22
2025-08-30 20:35:38.738 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-30 20:35:38.738 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-30 20:35:38.738 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-30 20:35:38.739 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-30 20:35:38.739 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 2.1ms
2025-08-30 20:35:38.739 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-30 20:35:38.739 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-30 20:35:38.741 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-30 20:35:38.741 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-30 20:35:38.741 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2332 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-30 20:35:38.742 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-30 20:35:38.742 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8665 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-08-30 20:35:38.742 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2307 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-30 20:35:38.743 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-30 20:35:38.743 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-30 20:35:38.743 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-30 20:35:38.743 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-08-30 20:35:38.743 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-30 20:35:38.744 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-30 20:35:38.744 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-30 20:35:38.744 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-30 20:35:38.744 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 1.1ms
2025-08-30 20:35:38.744 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-30 20:35:38.745 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-30 20:35:38.745 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-30 20:35:38.745 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-30 20:35:38.746 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2332 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-30 20:35:38.746 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8683 | 已显示标准空表格，表头数量: 22
2025-08-30 20:35:38.746 | INFO     | src.gui.prototype.prototype_main_window:__init__:3705 | 原型主窗口初始化完成
2025-08-30 20:35:39.031 | INFO     | __main__:main:514 | 应用程序启动成功
2025-08-30 20:35:39.035 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-30 20:35:39.035 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1474 | 执行延迟的自动选择最新数据...
2025-08-30 20:35:39.036 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-30 20:35:39.040 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-30 20:35:39.040 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2173 | 未找到有效的工资数据表
2025-08-30 20:35:39.041 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-30 20:35:39.041 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-30 20:35:39.041 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1280px)
2025-08-30 20:35:39.042 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2080 | MainWorkspaceArea 响应式适配: sm
2025-08-30 20:35:39.553 | INFO     | src.gui.prototype.prototype_main_window:delayed_heavy_fixes:9567 | UI亮度问题检测到并修复: ['table_opacity_in_stylesheet']
2025-08-30 20:35:39.553 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9477 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-08-30 20:35:39.554 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9491 | 🔧 [P0-1] 检测到亮度问题: ['table_opacity_in_stylesheet']
2025-08-30 20:35:39.554 | INFO     | src.gui.prototype.prototype_main_window:_apply_brightness_fixes:10025 | 🔧 [P1-2] 开始应用增强版亮度修复: ['table_opacity_in_stylesheet']
2025-08-30 20:35:39.575 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9497 | 🔧 [P0-1] 智能显示亮度修复完成
2025-08-30 20:35:40.040 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1396 | 🔧 [P1-2修复] 开始自动选择最新数据...
2025-08-30 20:35:40.041 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-30 20:35:40.043 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-30 20:35:40.043 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2173 | 未找到有效的工资数据表
2025-08-30 20:35:40.809 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:655 | 数据导入功能被触发，发出 import_requested 信号。
2025-08-30 20:35:40.810 | INFO     | src.gui.prototype.prototype_main_window:_get_suggested_target_path:8488 | 检测到当前在工资表TAB，生成工资表默认路径
2025-08-30 20:35:40.810 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:5907 | 接收到数据导入请求，推断的目标路径: 工资表 > 2025年 > 08月 > 全部在职人员。打开导入对话框。
2025-08-30 20:35:40.810 | INFO     | src.gui.prototype.prototype_main_window:_should_use_unified_interface:5924 | 🎯 直接使用新版统一数据导入界面
2025-08-30 20:35:40.825 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-30 20:35:40.826 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-30 20:35:40.826 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-30 20:35:40.826 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-30 20:35:40.827 | INFO     | src.modules.data_storage.database_manager:_initialize_database:102 | 开始初始化数据库...
2025-08-30 20:35:40.834 | INFO     | src.modules.data_storage.database_manager:_initialize_database:157 | 数据库初始化完成
2025-08-30 20:35:40.835 | INFO     | src.modules.data_storage.database_manager:__init__:98 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-08-30 20:35:40.835 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-30 20:35:40.842 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:107 | 动态表管理器初始化完成
2025-08-30 20:35:40.842 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-08-30 20:35:40.845 | INFO     | src.core.config_cache_manager:__init__:67 | 配置缓存管理器初始化完成: cache\config_cache, 最大条目数: 100
2025-08-30 20:35:40.845 | INFO     | src.modules.data_import.change_data_config_manager:__init__:69 | 配置缓存已启用
2025-08-30 20:35:40.845 | INFO     | src.modules.data_import.change_data_config_manager:__init__:75 | 配置管理器初始化完成，配置目录: C:\test\salary_changes\salary_changes\state\change_data_configs
2025-08-30 20:35:40.847 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:78 | 多Sheet导入器初始化完成
2025-08-30 20:35:40.865 | INFO     | src.gui.unified_config_manager:__init__:193 | 🔧 配置管理器初始化完成
2025-08-30 20:35:41.059 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-30 20:35:41.059 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-30 20:35:41.059 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2173 | 未找到有效的工资数据表
2025-08-30 20:35:41.059 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1612 | 🔧 [P1-2修复] 3次重试已用尽，可能数据导入尚未完成
2025-08-30 20:36:49.310 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:178 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-08-30 20:36:49.310 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-08-30 20:36:49.310 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-08-30 20:36:49.311 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-30 20:36:49.459 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 16列 (列过滤: 否)
2025-08-30 20:36:49.459 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-30 20:36:49.459 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-08-30 20:36:49.459 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 3行 x 16列
2025-08-30 20:36:49.464 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:599 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-30 20:36:49.465 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:642 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-08-30 20:36:49.466 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 2行 × 16列
2025-08-30 20:36:49.466 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-30 20:36:49.599 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-30 20:36:49.600 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 27 列 (原始 27 列)
2025-08-30 20:36:49.602 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-30 20:36:49.749 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-30 20:36:49.751 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 23 列 (原始 23 列)
2025-08-30 20:36:49.753 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-30 20:36:49.887 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-30 20:36:49.888 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-08-30 20:37:08.843 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:178 | 检测到工作表: ['起薪', '转正定级', '停薪-退休', '停薪-调离', '停薪-其他', '停薪-调整', '其他-考核扣款', '脱产读博', '请假扣款']
2025-08-30 20:37:08.844 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 5月异动人员5.7.xlsx (0.03MB)
2025-08-30 20:37:08.844 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 5月异动人员5.7.xlsx
2025-08-30 20:37:08.845 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\异动人员\5月异动人员5.7.xlsx: read
2025-08-30 20:37:08.894 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 14列 (列过滤: 否)
2025-08-30 20:37:08.895 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-30 20:37:08.896 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 14 列 (原始 14 列)
2025-08-30 20:37:08.897 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 2行 x 14列
2025-08-30 20:37:08.898 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 2行 × 14列
2025-08-30 20:37:08.898 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\异动人员\5月异动人员5.7.xlsx: read
2025-08-30 20:37:08.952 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-30 20:37:08.956 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 12 列 (原始 12 列)
2025-08-30 20:37:08.960 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\异动人员\5月异动人员5.7.xlsx: read
2025-08-30 20:37:09.019 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-30 20:37:09.020 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 11 列 (原始 11 列)
2025-08-30 20:37:09.023 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\异动人员\5月异动人员5.7.xlsx: read
2025-08-30 20:37:09.078 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-30 20:37:09.080 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 12 列 (原始 12 列)
2025-08-30 20:37:09.081 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\异动人员\5月异动人员5.7.xlsx: read
2025-08-30 20:37:09.135 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-30 20:37:09.137 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 12 列 (原始 12 列)
2025-08-30 20:37:09.140 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\异动人员\5月异动人员5.7.xlsx: read
2025-08-30 20:37:09.209 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-30 20:37:09.210 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 10 列 (原始 10 列)
2025-08-30 20:37:09.214 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:628 | 过滤记录[行1]: 工号字段(工作证号)为空, 数据: {'姓名': '无'}
2025-08-30 20:37:09.215 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:632 | 数据导入过滤: 发现1条工号为空的记录
2025-08-30 20:37:09.215 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:642 | 数据过滤完成: 原始1条记录，过滤1条无效记录，有效记录0条
2025-08-30 20:37:09.215 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\异动人员\5月异动人员5.7.xlsx: read
2025-08-30 20:37:09.281 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-30 20:37:09.283 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 9 列 (原始 9 列)
2025-08-30 20:37:09.284 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\异动人员\5月异动人员5.7.xlsx: read
2025-08-30 20:37:09.365 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-30 20:37:09.368 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 9 列 (原始 9 列)
2025-08-30 20:37:09.369 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\异动人员\5月异动人员5.7.xlsx: read
2025-08-30 20:37:09.434 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-30 20:37:09.437 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 12 列 (原始 12 列)
2025-08-30 20:37:11.811 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 5月异动人员5.7.xlsx (0.03MB)
2025-08-30 20:37:11.811 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 5月异动人员5.7.xlsx
2025-08-30 20:37:11.811 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\异动人员\5月异动人员5.7.xlsx: read
2025-08-30 20:37:11.865 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 14列 (列过滤: 否)
2025-08-30 20:37:11.865 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-30 20:37:11.866 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 14 列 (原始 14 列)
2025-08-30 20:37:11.867 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 1行 x 14列
2025-08-30 20:37:11.867 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 1行 × 14列
2025-08-30 20:37:43.815 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 5月异动人员5.7.xlsx (0.03MB)
2025-08-30 20:37:43.819 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 5月异动人员5.7.xlsx
2025-08-30 20:37:43.819 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\异动人员\5月异动人员5.7.xlsx: read
2025-08-30 20:37:43.882 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 12列 (列过滤: 否)
2025-08-30 20:37:43.882 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-30 20:37:43.885 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 12 列 (原始 12 列)
2025-08-30 20:37:43.885 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 1行 x 12列
2025-08-30 20:37:43.888 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 1行 × 12列
2025-08-30 20:37:47.011 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 5月异动人员5.7.xlsx (0.03MB)
2025-08-30 20:37:47.011 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 5月异动人员5.7.xlsx
2025-08-30 20:37:47.012 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\异动人员\5月异动人员5.7.xlsx: read
2025-08-30 20:37:47.062 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 11列 (列过滤: 否)
2025-08-30 20:37:47.063 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-30 20:37:47.064 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 11 列 (原始 11 列)
2025-08-30 20:37:47.064 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 1行 x 11列
2025-08-30 20:37:47.066 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 1行 × 11列
2025-08-30 20:37:51.779 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 5月异动人员5.7.xlsx (0.03MB)
2025-08-30 20:37:51.779 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 5月异动人员5.7.xlsx
2025-08-30 20:37:51.779 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\异动人员\5月异动人员5.7.xlsx: read
2025-08-30 20:37:51.834 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 12列 (列过滤: 否)
2025-08-30 20:37:51.835 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-30 20:37:51.837 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 12 列 (原始 12 列)
2025-08-30 20:37:51.837 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 1行 x 12列
2025-08-30 20:37:51.838 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 1行 × 12列
2025-08-30 20:37:56.986 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 5月异动人员5.7.xlsx (0.03MB)
2025-08-30 20:37:56.986 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 5月异动人员5.7.xlsx
2025-08-30 20:37:56.987 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\异动人员\5月异动人员5.7.xlsx: read
2025-08-30 20:37:57.034 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 9列 (列过滤: 否)
2025-08-30 20:37:57.035 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-30 20:37:57.036 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 9 列 (原始 9 列)
2025-08-30 20:37:57.036 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 1行 x 9列
2025-08-30 20:37:57.037 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 1行 × 9列
2025-08-30 20:38:10.275 | INFO     | src.gui.prototype.prototype_main_window:_show_unified_import_dialog:6019 | 用户取消了数据导入
2025-08-30 20:38:12.894 | INFO     | __main__:main:519 | 应用程序正常退出
2025-08-31 00:09:50.389 | INFO     | src.utils.log_config:_log_initialization_info:289 | 日志系统初始化完成
2025-08-31 00:09:50.389 | INFO     | src.utils.log_config:_log_initialization_info:290 | 日志级别: INFO
2025-08-31 00:09:50.389 | INFO     | src.utils.log_config:_log_initialization_info:291 | 控制台输出: True
2025-08-31 00:09:50.389 | INFO     | src.utils.log_config:_log_initialization_info:292 | 文件输出: True
2025-08-31 00:09:50.389 | INFO     | src.utils.log_config:_log_initialization_info:298 | 日志文件路径: logs/salary_system.log
2025-08-31 00:09:50.389 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-08-31 00:09:53.382 | INFO     | src.gui.style_manager:__init__:68 | StyleManager初始化完成
2025-08-31 00:09:53.385 | INFO     | src.gui.style_manager:apply_global_style:93 | 开始应用全局Material Design样式...
2025-08-31 00:09:53.385 | INFO     | src.gui.style_manager:apply_global_style:105 | 全局样式应用成功
2025-08-31 00:09:53.385 | INFO     | src.gui.style_manager:enable_hot_reload:416 | 样式热重载未启用（非开发环境）
2025-08-31 00:09:53.385 | INFO     | __main__:setup_app_logging:423 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-08-31 00:09:53.385 | INFO     | __main__:main:487 | 初始化核心管理器...
2025-08-31 00:09:53.385 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-31 00:09:53.385 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-31 00:09:53.385 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-31 00:09:53.385 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-31 00:09:53.389 | INFO     | src.modules.data_storage.database_manager:_initialize_database:102 | 开始初始化数据库...
2025-08-31 00:09:53.400 | INFO     | src.modules.data_storage.database_manager:_initialize_database:157 | 数据库初始化完成
2025-08-31 00:09:53.400 | INFO     | src.modules.data_storage.database_manager:__init__:98 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\bak31\salary_changes\data\db\salary_system.db
2025-08-31 00:09:53.400 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-31 00:09:53.407 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:107 | 动态表管理器初始化完成
2025-08-31 00:09:53.407 | INFO     | __main__:main:492 | 核心管理器初始化完成。
2025-08-31 00:09:53.407 | INFO     | src.core.data_source_unification_manager:__init__:128 | 数据源统一管理器初始化完成
2025-08-31 00:09:53.407 | INFO     | src.core.table_sort_state_manager:__init__:177 | 表级排序状态管理器初始化完成
2025-08-31 00:09:53.407 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-31 00:09:53.407 | INFO     | src.core.error_handler_manager:__init__:111 | 🔧 [P2-3] 错误处理管理器初始化完成
2025-08-31 00:09:53.415 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: data_error_medium
2025-08-31 00:09:53.415 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: ui_error_medium
2025-08-31 00:09:53.415 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: system_error_medium
2025-08-31 00:09:53.417 | INFO     | src.gui.prototype.prototype_main_window:_register_recovery_strategies:11931 | 🔧 [P2-3] 错误恢复策略注册完成
2025-08-31 00:09:53.417 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-31 00:09:53.419 | INFO     | src.gui.prototype.prototype_main_window:_setup_error_handling:11786 | 🔧 [P2-3] 错误处理机制设置完成
2025-08-31 00:09:53.419 | INFO     | src.gui.prototype.prototype_main_window:_init_recursive_call_protection:11824 | 🔧 [P1-2] 递归调用防护机制初始化完成
2025-08-31 00:09:53.589 | INFO     | src.core.architecture_factory:__init__:62 | 架构重构工厂初始化完成
2025-08-31 00:09:53.589 | INFO     | src.core.architecture_factory:initialize_architecture:72 | 开始初始化架构重构系统...
2025-08-31 00:09:53.589 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-31 00:09:53.596 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:153 | 使用已存在的配置文件: state/data/field_mappings.json
2025-08-31 00:09:53.596 | INFO     | src.modules.data_import.config_sync_manager:__init__:81 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-08-31 00:09:53.596 | INFO     | src.core.unified_state_manager:_load_state:585 | 状态已从文件加载: state/unified_state.json
2025-08-31 00:09:53.596 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-31 00:09:53.596 | INFO     | src.core.field_mapping_manager:_load_config:89 | 🔧 [P3优化] 字段映射配置加载成功
2025-08-31 00:09:53.596 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:153 | 使用已存在的配置文件: state/data/field_mappings.json
2025-08-31 00:09:53.596 | INFO     | src.modules.data_import.config_sync_manager:__init__:81 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-08-31 00:09:53.596 | INFO     | src.modules.format_management.field_registry:__init__:95 | 🏷️ [字段注册] 字段注册系统初始化: state/data/field_mappings.json
2025-08-31 00:09:53.596 | INFO     | src.core.unified_mapping_service:__init__:45 | UnifiedMappingService 初始化完成
2025-08-31 00:09:53.596 | INFO     | src.core.unified_data_request_manager:__init__:217 | 统一数据请求管理器初始化完成
2025-08-31 00:09:53.596 | INFO     | src.core.unified_data_request_manager:__init__:217 | 统一数据请求管理器初始化完成
2025-08-31 00:09:53.596 | INFO     | src.core.unified_state_manager:_load_state:585 | 状态已从文件加载: state/unified_state.json
2025-08-31 00:09:53.596 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-31 00:09:53.596 | INFO     | src.services.table_data_service:__init__:84 | 表格数据服务初始化完成
2025-08-31 00:09:53.607 | INFO     | src.core.architecture_factory:initialize_architecture:103 | 🎉 架构重构系统初始化成功！耗时: 17.7ms
2025-08-31 00:09:53.634 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:46 | 数据预加载缓存初始化完成 - 最大条目数: 100, TTL: 300秒
2025-08-31 00:09:53.634 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:232 | 表格状态缓存初始化完成
2025-08-31 00:09:53.634 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:32 | 表头配置缓存初始化完成
2025-08-31 00:09:53.634 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:211 | 字段映射缓存初始化完成
2025-08-31 00:09:53.634 | INFO     | src.gui.prototype.performance.performance_manager:__init__:46 | 🚀 性能管理器初始化完成
2025-08-31 00:09:53.640 | INFO     | src.gui.prototype.prototype_main_window:__init__:3651 | 🚀 性能管理器已集成
2025-08-31 00:09:53.640 | INFO     | src.gui.prototype.prototype_main_window:__init__:3653 | ✅ 新架构集成成功！
2025-08-31 00:09:53.640 | INFO     | src.gui.prototype.prototype_main_window:_inject_config_sync_manager_to_existing_tables:3766 | ConfigSyncManager重新注入完成，已更新0个表格实例
2025-08-31 00:09:53.640 | INFO     | src.gui.prototype.prototype_main_window:_setup_new_architecture_listeners:3731 | ✅ 新架构事件监听器设置完成
2025-08-31 00:09:53.649 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-08-31 00:09:53.649 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-08-31 00:09:53.649 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-08-31 00:09:53.896 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:2727 | 菜单栏创建完成
2025-08-31 00:09:53.906 | INFO     | src.gui.prototype.prototype_main_window:__init__:2702 | 菜单栏管理器初始化完成
2025-08-31 00:09:53.906 | INFO     | src.gui.table_header_manager:__init__:105 | 🔧 [P1-3] 性能优化表头管理器初始化完成
2025-08-31 00:09:53.906 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:5459 | 管理器设置完成，包含增强版表头管理器
2025-08-31 00:09:53.906 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5464 | 🔧 开始应用窗口级Material Design样式...
2025-08-31 00:09:53.906 | INFO     | src.gui.style_manager:apply_window_style:123 | 开始应用窗口级样式...
2025-08-31 00:09:53.913 | INFO     | src.gui.style_manager:apply_window_style:136 | 窗口级样式应用成功
2025-08-31 00:09:53.913 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5471 | ✅ 窗口级样式应用成功
2025-08-31 00:09:53.913 | INFO     | src.gui.prototype.prototype_main_window:_setup_responsive_style:5512 | ✅ 响应式样式监听设置完成
2025-08-31 00:09:53.919 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-31 00:09:53.919 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-31 00:09:53.919 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-31 00:09:53.924 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:165 | 导航状态加载成功
2025-08-31 00:09:53.935 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-08-31 00:09:53.939 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-08-31 00:09:53.947 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-31 00:09:53.947 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1992 | 元数据项 salary_data_2025_05 缺少年份或月份信息，已跳过
2025-08-31 00:09:53.963 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_fallback_salary_data:1835 | 🏠 使用示例数据结构加载导航（首次启动或暂无数据时显示）
2025-08-31 00:09:53.968 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 4 个匹配类型 'change_data' 的表 (尝试 1/5)
2025-08-31 00:09:53.970 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-31 00:09:53.970 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:924 | 恢复导航状态: 4个展开项
2025-08-31 00:09:53.970 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > 全部在职人员', '工资表', '工资表 > 2025年', '工资表 > 2025年 > 05月']
2025-08-31 00:09:53.974 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-08-31 00:09:53.980 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:165 | 导航状态加载成功
2025-08-31 00:09:53.981 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-08-31 00:09:53.984 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-08-31 00:09:53.992 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-31 00:09:53.996 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1992 | 元数据项 salary_data_2025_05 缺少年份或月份信息，已跳过
2025-08-31 00:09:54.002 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 4 个匹配类型 'change_data' 的表 (尝试 1/5)
2025-08-31 00:09:54.005 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-31 00:09:54.014 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['异动人员表 > 2025年 > 12月 > A岗职工', '异动人员表 > 2025年 > 12月 > 离休人员工资表', '异动人员表 > 2025年 > 12月', '异动人员表 > 2025年 > 12月 > 退休人员工资表', '异动人员表 > 2025年']
2025-08-31 00:09:54.015 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['异动人员表 > 2025年 > 12月 > A岗职工', '异动人员表 > 2025年 > 12月 > 离休人员工资表', '异动人员表 > 2025年 > 12月', '异动人员表 > 2025年 > 12月 > 退休人员工资表', '异动人员表 > 2025年']
2025-08-31 00:09:54.016 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-08-31 00:09:54.017 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1396 | 🔧 [P1-2修复] 开始自动选择最新数据...
2025-08-31 00:09:54.039 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-31 00:09:54.042 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-31 00:09:54.043 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2173 | 未找到有效的工资数据表
2025-08-31 00:09:54.044 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1601 | 🔧 [P1-2修复] 检查表状态失败，1.0s后重试...
2025-08-31 00:09:54.045 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1405 | 🔧 [P1-2修复] 未找到最新工资数据路径（可能是首次启动）
2025-08-31 00:09:54.370 | INFO     | src.gui.prototype.widgets.header_update_manager:__init__:68 | HeaderUpdateManager 初始化完成，关联表格: VirtualizedExpandableTable
2025-08-31 00:09:54.371 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2142 | 🚨 [架构修复] 启用模型数据同步机制
2025-08-31 00:09:54.376 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1366 | 快捷键注册完成: 18/18 个
2025-08-31 00:09:54.377 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1809 | 拖拽排序管理器初始化完成
2025-08-31 00:09:54.403 | INFO     | src.modules.data_management.data_flow_validator:__init__:85 | 🔧 [数据验证器] 初始化完成，验证级别: lenient
2025-08-31 00:09:54.405 | INFO     | src.modules.state_management.table_state_manager:__init__:103 | 🔧 [状态管理] 仅使用内存缓存
2025-08-31 00:09:54.410 | INFO     | src.modules.state_management.table_state_manager:__init__:105 | 🔧 [状态管理] 初始化完成，最大缓存: 100
2025-08-31 00:09:54.413 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2195 | 🔧 [P0-紧急修复] 数据流验证器初始化成功（宽松模式）
2025-08-31 00:09:54.414 | INFO     | src.modules.data_import.header_edit_manager:__init__:82 | 表头编辑管理器初始化完成
2025-08-31 00:09:54.418 | INFO     | src.core.unified_state_manager:_load_state:585 | 状态已从文件加载: state/unified_state.json
2025-08-31 00:09:54.430 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-31 00:09:54.431 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2247 | 🎯 [统一格式管理] 使用统一格式管理器
2025-08-31 00:09:54.444 | INFO     | src.gui.prototype.widgets.column_sort_manager:_load_field_mappings_from_config:370 | 🔧 [新架构] 成功加载 46 个字段映射
2025-08-31 00:09:54.445 | INFO     | src.gui.prototype.widgets.column_sort_manager:__init__:102 | 🆕 [新架构排序] 多列排序管理器初始化完成，最大排序列数: 3
2025-08-31 00:09:54.447 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2294 | 🆕 [新架构多列排序] 初始化完成，支持最多3列排序
2025-08-31 00:09:54.453 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1553 | 🔧 [列宽保存修复] 列宽管理器初始化完成
2025-08-31 00:09:54.456 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1554 | 🔧 [列宽保存修复] 配置文件绝对路径: C:\test\salary_changes\bak31\salary_changes\state\column_widths.json
2025-08-31 00:09:54.456 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1555 | 🔧 [列宽保存修复] 配置文件存在: True
2025-08-31 00:09:54.457 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1556 | 🔧 [列宽保存修复] 父目录存在: True
2025-08-31 00:09:54.457 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1557 | 🔧 [列宽保存修复] 当前工作目录: C:\test\salary_changes\bak31\salary_changes
2025-08-31 00:09:54.458 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2301 | 列宽管理器初始化完成
2025-08-31 00:09:54.459 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:setup_ui:2428 | 🔧 [架构修复] 保持自定义排序功能，修复数据显示问题
2025-08-31 00:09:54.463 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2315 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-08-31 00:09:54.463 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2307 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-31 00:09:54.464 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-31 00:09:54.469 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-31 00:09:54.479 | INFO     | src.gui.prototype.widgets.pagination_state_manager:__init__:78 | 🔧 [P1优化] 分页状态管理器初始化完成
2025-08-31 00:09:54.492 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7339 | 方案A：安全设置列数: 0
2025-08-31 00:09:54.493 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2838 | 🔧 [P1优化] 表切换操作: None -> default_table
2025-08-31 00:09:54.493 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-31 00:09:54.501 | INFO     | src.gui.prototype.widgets.table_header_cache:__init__:67 | 🔧 [P1优化] 表头缓存管理器初始化完成，最大缓存: 100
2025-08-31 00:09:54.504 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2890 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-31 00:09:54.505 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-08-31 00:09:54.519 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-31 00:09:54.520 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-31 00:09:54.521 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-31 00:09:54.522 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-31 00:09:54.523 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:443 | 表格数据已设置: 0 行, 22 列
2025-08-31 00:09:54.526 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:443 | 🔧 [新架构] 排序管理器切换到表格: default_table
2025-08-31 00:09:54.531 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 56.1ms
2025-08-31 00:09:54.532 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-31 00:09:54.533 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-31 00:09:54.536 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-31 00:09:54.539 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-31 00:09:54.575 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2332 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-31 00:09:54.583 | INFO     | src.gui.widgets.pagination_widget:_apply_styles:298 | 分页组件Material Design样式应用成功
2025-08-31 00:09:54.592 | INFO     | src.gui.widgets.pagination_widget:__init__:176 | ✅ [防抖升级] 智能防抖系统已启用
2025-08-31 00:09:54.593 | INFO     | src.gui.widgets.pagination_widget:__init__:184 | 分页组件初始化完成
2025-08-31 00:09:54.640 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:595 | 控制面板按钮信号连接完成
2025-08-31 00:09:54.685 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:5421 | 快捷键设置完成
2025-08-31 00:09:54.685 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:5378 | 主窗口UI设置完成。
2025-08-31 00:09:54.689 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5615 | 🔧 [全局排序] 全局排序开关连接成功
2025-08-31 00:09:54.690 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5647 | 🆕 [新架构排序] 使用表格组件内部的自定义排序循环，无需连接排序信号
2025-08-31 00:09:54.691 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5659 | ✅ 已连接分页刷新信号到主窗口
2025-08-31 00:09:54.694 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5660 | ✅ 已连接分页组件事件到新架构
2025-08-31 00:09:54.697 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5671 | ✅ 已连接概览标签页刷新按钮到主窗口
2025-08-31 00:09:54.701 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5674 | 信号连接设置完成
2025-08-31 00:09:54.703 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6963 | 🔧 [P1-2修复] 发现 6 个表的配置
2025-08-31 00:09:54.704 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:488 | 🔧 [P1-1修复] 加载统一格式映射: salary_data_2025_07_active_employees, 10个字段
2025-08-31 00:09:54.706 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:488 | 🔧 [P1-1修复] 加载统一格式映射: active_employees, 5个字段
2025-08-31 00:09:54.707 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: change_data_2025_12_全部在职人员工资表, 30个字段
2025-08-31 00:09:54.707 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: change_data_2025_12_退休人员工资表, 30个字段
2025-08-31 00:09:54.714 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: change_data_2025_12_A岗职工, 30个字段
2025-08-31 00:09:54.714 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: change_data_2025_12_离休人员工资表, 30个字段
2025-08-31 00:09:54.715 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6973 | ✅ [P1-2修复] 已加载字段映射信息，共6个表的映射
2025-08-31 00:09:54.725 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2307 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-31 00:09:54.728 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-31 00:09:54.729 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-31 00:09:54.730 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-31 00:09:54.732 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-08-31 00:09:54.732 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7339 | 方案A：安全设置列数: 22
2025-08-31 00:09:54.733 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-31 00:09:54.735 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-31 00:09:54.736 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-31 00:09:54.737 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-31 00:09:54.746 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 15.9ms
2025-08-31 00:09:54.748 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-31 00:09:54.749 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-31 00:09:54.751 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-31 00:09:54.758 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-31 00:09:54.761 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2332 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-31 00:09:54.762 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-31 00:09:54.763 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8665 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-08-31 00:09:54.764 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2307 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-31 00:09:54.765 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-31 00:09:54.765 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-31 00:09:54.766 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-31 00:09:54.767 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-08-31 00:09:54.769 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-31 00:09:54.770 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-31 00:09:54.777 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-31 00:09:54.777 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-31 00:09:54.779 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 12.6ms
2025-08-31 00:09:54.779 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-31 00:09:54.780 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-31 00:09:54.782 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-31 00:09:54.782 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-31 00:09:54.783 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2332 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-31 00:09:54.786 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8683 | 已显示标准空表格，表头数量: 22
2025-08-31 00:09:54.792 | INFO     | src.gui.prototype.prototype_main_window:__init__:3705 | 原型主窗口初始化完成
2025-08-31 00:09:54.827 | INFO     | __main__:main:514 | 应用程序启动成功
2025-08-31 00:09:54.836 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-31 00:09:54.836 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1474 | 执行延迟的自动选择最新数据...
2025-08-31 00:09:54.936 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-31 00:09:54.937 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-31 00:09:55.017 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1280px)
2025-08-31 00:09:55.018 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2080 | MainWorkspaceArea 响应式适配: sm
2025-08-31 00:09:55.118 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-31 00:09:55.121 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-31 00:09:55.123 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2173 | 未找到有效的工资数据表
2025-08-31 00:09:55.526 | INFO     | src.gui.prototype.prototype_main_window:delayed_heavy_fixes:9567 | UI亮度问题检测到并修复: ['table_opacity_in_stylesheet']
2025-08-31 00:09:55.527 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9477 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-08-31 00:09:55.530 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9491 | 🔧 [P0-1] 检测到亮度问题: ['table_opacity_in_stylesheet']
2025-08-31 00:09:55.531 | INFO     | src.gui.prototype.prototype_main_window:_apply_brightness_fixes:10025 | 🔧 [P1-2] 开始应用增强版亮度修复: ['table_opacity_in_stylesheet']
2025-08-31 00:09:55.551 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9497 | 🔧 [P0-1] 智能显示亮度修复完成
2025-08-31 00:09:56.126 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1396 | 🔧 [P1-2修复] 开始自动选择最新数据...
2025-08-31 00:09:56.126 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-31 00:09:56.129 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-31 00:09:56.129 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2173 | 未找到有效的工资数据表
2025-08-31 00:09:57.131 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-31 00:09:57.131 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-31 00:09:57.134 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2173 | 未找到有效的工资数据表
2025-08-31 00:09:57.134 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1612 | 🔧 [P1-2修复] 3次重试已用尽，可能数据导入尚未完成
2025-08-31 00:09:59.795 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:655 | 数据导入功能被触发，发出 import_requested 信号。
2025-08-31 00:09:59.795 | INFO     | src.gui.prototype.prototype_main_window:_get_suggested_target_path:8488 | 检测到当前在工资表TAB，生成工资表默认路径
2025-08-31 00:09:59.795 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:5907 | 接收到数据导入请求，推断的目标路径: 工资表 > 2025年 > 08月 > 全部在职人员。打开导入对话框。
2025-08-31 00:09:59.805 | INFO     | src.gui.prototype.prototype_main_window:_should_use_unified_interface:5924 | 🎯 直接使用新版统一数据导入界面
2025-08-31 00:09:59.896 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-31 00:09:59.896 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-31 00:09:59.896 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-31 00:09:59.896 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-31 00:09:59.896 | INFO     | src.modules.data_storage.database_manager:_initialize_database:102 | 开始初始化数据库...
2025-08-31 00:09:59.906 | INFO     | src.modules.data_storage.database_manager:_initialize_database:157 | 数据库初始化完成
2025-08-31 00:09:59.906 | INFO     | src.modules.data_storage.database_manager:__init__:98 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\bak31\salary_changes\data\db\salary_system.db
2025-08-31 00:09:59.906 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-31 00:09:59.913 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:107 | 动态表管理器初始化完成
2025-08-31 00:09:59.913 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-08-31 00:09:59.919 | INFO     | src.core.config_cache_manager:__init__:67 | 配置缓存管理器初始化完成: cache\config_cache, 最大条目数: 100
2025-08-31 00:09:59.919 | INFO     | src.modules.data_import.change_data_config_manager:__init__:69 | 配置缓存已启用
2025-08-31 00:09:59.919 | INFO     | src.modules.data_import.change_data_config_manager:__init__:75 | 配置管理器初始化完成，配置目录: C:\test\salary_changes\bak31\salary_changes\state\change_data_configs
2025-08-31 00:09:59.919 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:78 | 多Sheet导入器初始化完成
2025-08-31 00:09:59.963 | INFO     | src.gui.unified_config_manager:__init__:193 | 🔧 配置管理器初始化完成
2025-08-31 00:10:06.911 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:178 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-08-31 00:10:06.912 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-08-31 00:10:06.915 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-08-31 00:10:06.916 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-31 00:10:07.023 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 16列 (列过滤: 否)
2025-08-31 00:10:07.027 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-31 00:10:07.030 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-08-31 00:10:07.030 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 3行 x 16列
2025-08-31 00:10:07.039 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:599 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-31 00:10:07.042 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:642 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-08-31 00:10:07.043 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 2行 × 16列
2025-08-31 00:10:07.046 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-31 00:10:07.155 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-31 00:10:07.156 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 27 列 (原始 27 列)
2025-08-31 00:10:07.160 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-31 00:10:07.265 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-31 00:10:07.268 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 23 列 (原始 23 列)
2025-08-31 00:10:07.271 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-31 00:10:07.377 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-31 00:10:07.379 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-08-31 00:10:10.877 | INFO     | src.gui.prototype.prototype_main_window:_show_unified_import_dialog:6019 | 用户取消了数据导入
2025-08-31 00:10:12.310 | INFO     | __main__:main:519 | 应用程序正常退出
