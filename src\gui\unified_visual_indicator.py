#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一配置可视化指示器

为不同配置来源提供可视化指示效果，包括：
- 颜色编码系统
- 图标指示系统
- 优先级标识
- 交互提示系统
- 表格单元格样式

创建时间: 2025-01-20
方案: 统一配置界面详细设计（方案3）
"""

import sys
import os
from typing import Dict, List, Optional, Any, Tuple
from PyQt5.QtWidgets import (
    QWidget, QTableWidget, QTableWidgetItem, QLabel, QPushButton,
    QComboBox, QCheckBox, QLineEdit, QTextEdit, QTreeWidget,
    QTreeWidgetItem, QHeaderView, QMenu, QAction, QToolTip,
    QApplication, QStyle, QStyleOption
)
from PyQt5.QtCore import Qt, QPoint, QRect, QTimer, pyqtSignal
from PyQt5.QtGui import (
    QColor, QPalette, QFont, QIcon, QPixmap, QPainter, QBrush,
    QPen, QLinearGradient, QRadialGradient, QPolygon, QCursor
)

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from src.utils.log_config import setup_logger
from src.gui.unified_config_manager import ConfigurationSource, ConfigurationItem, ConflictReport


class SourceVisualConfig:
    """配置来源的视觉配置"""
    
    def __init__(self, color: str, icon: str, priority: int, description: str,
                 bg_color: str = None, text_color: str = None):
        self.color = color                    # 主色调
        self.icon = icon                      # 图标字符
        self.priority = priority              # 优先级
        self.description = description        # 描述
        self.bg_color = bg_color or color     # 背景色
        self.text_color = text_color or "#ffffff"  # 文字颜色


class VisualSourceIndicator:
    """可视化配置来源指示器类"""
    
    def __init__(self):
        """初始化可视化指示器"""
        self.logger = setup_logger(__name__)
        
        # 配置来源的视觉标识
        self.source_configs = {
            ConfigurationSource.SYSTEM_DEFAULT: SourceVisualConfig(
                color="#2196F3",         # 蓝色
                icon="🏭",
                priority=4,
                description="系统内置的默认配置",
                bg_color="#E3F2FD",
                text_color="#1565C0"
            ),
            ConfigurationSource.TABLE_TEMPLATE: SourceVisualConfig(
                color="#FF9800",         # 橙色
                icon="📋",
                priority=3,
                description="基于表类型的模板配置",
                bg_color="#FFF3E0",
                text_color="#E65100"
            ),
            ConfigurationSource.USER_CONFIG: SourceVisualConfig(
                color="#4CAF50",         # 绿色
                icon="👤",
                priority=1,
                description="用户自定义的配置",
                bg_color="#E8F5E8",
                text_color="#2E7D32"
            ),
            ConfigurationSource.TEMPORARY_OVERRIDE: SourceVisualConfig(
                color="#F44336",         # 红色
                icon="⚡",
                priority=2,
                description="临时覆盖的配置",
                bg_color="#FFEBEE",
                text_color="#C62828"
            )
        }
        
        # 冲突警告配置
        self.conflict_config = SourceVisualConfig(
            color="#FF5722",
            icon="⚠️",
            priority=0,
            description="存在配置冲突",
            bg_color="#FFF3E0",
            text_color="#D84315"
        )
        
        # 未配置状态
        self.not_configured = SourceVisualConfig(
            color="#9E9E9E",
            icon="❓",
            priority=999,
            description="未配置",
            bg_color="#F5F5F5",
            text_color="#616161"
        )
        
        self.logger.info("🎨 可视化指示器初始化完成")
    
    def get_source_config(self, source: ConfigurationSource) -> SourceVisualConfig:
        """获取配置来源的视觉配置"""
        return self.source_configs.get(source, self.not_configured)
    
    def apply_source_styling(self, widget: QWidget, source: ConfigurationSource, 
                           has_conflict: bool = False) -> bool:
        """应用配置来源的视觉样式
        
        Args:
            widget: 要应用样式的控件
            source: 配置来源
            has_conflict: 是否存在冲突
            
        Returns:
            是否成功应用样式
        """
        try:
            if has_conflict:
                config = self.conflict_config
            else:
                config = self.get_source_config(source)
            
            # 根据控件类型应用不同的样式
            if isinstance(widget, QTableWidgetItem):
                self._apply_table_item_style(widget, config, has_conflict)
            elif isinstance(widget, QTableWidget):
                self._apply_table_widget_style(widget, config, has_conflict)
            elif isinstance(widget, QLabel):
                self._apply_label_style(widget, config, has_conflict)
            elif isinstance(widget, QPushButton):
                self._apply_button_style(widget, config, has_conflict)
            elif isinstance(widget, (QComboBox, QLineEdit)):
                self._apply_input_style(widget, config, has_conflict)
            elif isinstance(widget, QTreeWidgetItem):
                self._apply_tree_item_style(widget, config, has_conflict)
            else:
                # 通用样式应用
                self._apply_generic_style(widget, config, has_conflict)
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 应用样式失败: {e}")
            return False
    
    def _apply_table_item_style(self, item: QTableWidgetItem, config: SourceVisualConfig, has_conflict: bool):
        """应用表格项样式"""
        # 设置背景色
        item.setBackground(QColor(config.bg_color))
        
        # 设置文字颜色
        item.setForeground(QColor(config.text_color))
        
        # 设置字体样式
        font = item.font()
        if config.priority == 1:  # 用户配置使用粗体
            font.setBold(True)
        elif config.priority == 3:  # 模板配置使用斜体
            font.setItalic(True)
        elif has_conflict:  # 冲突配置使用删除线
            font.setStrikeOut(True)
        
        item.setFont(font)
        
        # 设置提示文本
        tooltip = f"{config.icon} {config.description}"
        if has_conflict:
            tooltip = f"⚠️ 存在配置冲突 - {tooltip}"
        item.setToolTip(tooltip)
        
        # 设置用户数据，用于后续处理
        item.setData(Qt.UserRole, {
            'source': config,
            'has_conflict': has_conflict
        })
    
    def _apply_table_widget_style(self, table: QTableWidget, config: SourceVisualConfig, has_conflict: bool):
        """应用表格控件样式"""
        # 设置边框样式
        border_style = "solid" if config.priority <= 2 else "dashed"
        if has_conflict:
            border_style = "dotted"
        
        style = f"""
            QTableWidget {{
                border: 2px {border_style} {config.color};
                border-radius: 4px;
                background-color: {config.bg_color};
                gridline-color: {config.color};
            }}
            QTableWidget::item {{
                border-right: 1px solid {config.color};
                border-bottom: 1px solid {config.color};
                color: {config.text_color};
            }}
            QTableWidget::item:selected {{
                background-color: {config.color};
                color: white;
            }}
        """
        table.setStyleSheet(style)
    
    def _apply_label_style(self, label: QLabel, config: SourceVisualConfig, has_conflict: bool):
        """应用标签样式"""
        # 创建带图标的文本
        original_text = label.text()
        if not original_text.startswith(config.icon):
            label.setText(f"{config.icon} {original_text}")
        
        # 设置样式
        style = f"""
            QLabel {{
                color: {config.text_color};
                background-color: {config.bg_color};
                border: 1px solid {config.color};
                border-radius: 3px;
                padding: 2px 6px;
                font-weight: {'bold' if config.priority == 1 else 'normal'};
            }}
        """
        
        if has_conflict:
            style += """
                QLabel {
                    animation: blink 1s infinite;
                }
            """
        
        label.setStyleSheet(style)
        label.setToolTip(config.description)
    
    def _apply_button_style(self, button: QPushButton, config: SourceVisualConfig, has_conflict: bool):
        """应用按钮样式"""
        # 设置按钮文本（如果还没有图标）
        if not button.text().startswith(config.icon):
            button.setText(f"{config.icon} {button.text()}")
        
        style = f"""
            QPushButton {{
                background-color: {config.color};
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {self._darken_color(config.color, 0.1)};
            }}
            QPushButton:pressed {{
                background-color: {self._darken_color(config.color, 0.2)};
            }}
        """
        
        if has_conflict:
            style += f"""
                QPushButton {{
                    border: 2px solid {self.conflict_config.color};
                    animation: pulse 1s infinite;
                }}
            """
        
        button.setStyleSheet(style)
        button.setToolTip(config.description)
    
    def _apply_input_style(self, input_widget: QWidget, config: SourceVisualConfig, has_conflict: bool):
        """应用输入控件样式"""
        border_style = "solid" if not has_conflict else "dashed"
        
        style = f"""
            {input_widget.__class__.__name__} {{
                border: 2px {border_style} {config.color};
                border-radius: 4px;
                background-color: {config.bg_color};
                color: {config.text_color};
                padding: 4px;
            }}
            {input_widget.__class__.__name__}:focus {{
                border-color: {self._brighten_color(config.color, 0.2)};
                background-color: white;
            }}
        """
        
        input_widget.setStyleSheet(style)
        input_widget.setToolTip(config.description)
    
    def _apply_tree_item_style(self, item: QTreeWidgetItem, config: SourceVisualConfig, has_conflict: bool):
        """应用树形项样式"""
        # 设置背景色
        for column in range(item.columnCount()):
            item.setBackground(column, QColor(config.bg_color))
            item.setForeground(column, QColor(config.text_color))
        
        # 设置图标（如果第一列没有图标文本）
        if item.columnCount() > 0:
            text = item.text(0)
            if not text.startswith(config.icon):
                item.setText(0, f"{config.icon} {text}")
        
        # 设置字体
        font = item.font(0)
        if config.priority == 1:
            font.setBold(True)
        elif has_conflict:
            font.setStrikeOut(True)
        
        for column in range(item.columnCount()):
            item.setFont(column, font)
    
    def _apply_generic_style(self, widget: QWidget, config: SourceVisualConfig, has_conflict: bool):
        """应用通用样式"""
        border_style = "solid" if not has_conflict else "dashed"
        
        style = f"""
            {widget.__class__.__name__} {{
                border: 1px {border_style} {config.color};
                border-radius: 3px;
                background-color: {config.bg_color};
                color: {config.text_color};
            }}
        """
        
        widget.setStyleSheet(style)
        widget.setToolTip(config.description)
    
    def show_conflict_warning(self, widget: QWidget, conflicts: List[ConflictReport]):
        """显示配置冲突警告
        
        Args:
            widget: 要显示警告的控件
            conflicts: 冲突报告列表
        """
        if not conflicts:
            return
        
        # 创建冲突提示文本
        conflict_texts = []
        for conflict in conflicts:
            conflict_texts.append(conflict.get_conflict_summary())
        
        warning_text = "⚠️ 配置冲突警告:\n" + "\n".join(conflict_texts)
        
        # 显示提示
        if hasattr(widget, 'setToolTip'):
            widget.setToolTip(warning_text)
        
        # 应用冲突样式
        self.apply_source_styling(widget, ConfigurationSource.USER_CONFIG, has_conflict=True)
        
        # 创建动画效果（闪烁边框）
        self._add_blink_animation(widget)
    
    def create_source_indicator_label(self, source: ConfigurationSource, 
                                    text: str = "", has_conflict: bool = False) -> QLabel:
        """创建配置来源指示标签
        
        Args:
            source: 配置来源
            text: 额外的文本
            has_conflict: 是否存在冲突
            
        Returns:
            配置好样式的标签
        """
        config = self.get_source_config(source)
        
        # 创建标签文本
        label_text = f"{config.icon}"
        if text:
            label_text += f" {text}"
        if has_conflict:
            label_text = f"⚠️ {label_text}"
        
        label = QLabel(label_text)
        self.apply_source_styling(label, source, has_conflict)
        
        return label
    
    def create_priority_indicator(self, source: ConfigurationSource) -> QLabel:
        """创建优先级指示器
        
        Args:
            source: 配置来源
            
        Returns:
            优先级指示标签
        """
        config = self.get_source_config(source)
        
        # 优先级图标映射
        priority_icons = {
            1: "🏆",  # 最高优先级
            2: "🥈",  # 高优先级
            3: "🥉",  # 中优先级
            4: "📋"   # 低优先级
        }
        
        icon = priority_icons.get(config.priority, "❓")
        text = f"{icon} P{config.priority}"
        
        label = QLabel(text)
        label.setToolTip(f"优先级 {config.priority}: {config.description}")
        
        # 设置优先级标识样式
        style = f"""
            QLabel {{
                background-color: {config.bg_color};
                color: {config.text_color};
                border: 1px solid {config.color};
                border-radius: 12px;
                padding: 2px 8px;
                font-size: 10px;
                font-weight: bold;
            }}
        """
        label.setStyleSheet(style)
        
        return label
    
    def create_conflict_indicator(self, conflicts: List[ConflictReport]) -> QLabel:
        """创建冲突指示器
        
        Args:
            conflicts: 冲突报告列表
            
        Returns:
            冲突指示标签
        """
        if not conflicts:
            label = QLabel("✅ 无冲突")
            label.setStyleSheet("""
                QLabel {
                    background-color: #E8F5E8;
                    color: #2E7D32;
                    border: 1px solid #4CAF50;
                    border-radius: 3px;
                    padding: 2px 6px;
                    font-size: 11px;
                }
            """)
        else:
            label = QLabel(f"⚠️ {len(conflicts)} 个冲突")
            label.setStyleSheet("""
                QLabel {
                    background-color: #FFEBEE;
                    color: #C62828;
                    border: 1px solid #F44336;
                    border-radius: 3px;
                    padding: 2px 6px;
                    font-size: 11px;
                    font-weight: bold;
                }
            """)
            
            # 创建详细的冲突提示
            conflict_details = []
            for conflict in conflicts:
                conflict_details.append(conflict.get_conflict_summary())
            
            label.setToolTip("配置冲突详情:\n" + "\n".join(conflict_details))
        
        return label
    
    def setup_interactive_tooltips(self, widget: QWidget, config_item: ConfigurationItem, 
                                 conflicts: List[ConflictReport] = None):
        """设置交互式提示
        
        Args:
            widget: 控件
            config_item: 配置项
            conflicts: 冲突列表
        """
        # 创建详细的提示信息
        tooltip_lines = [
            f"📋 配置项: {config_item.key}",
            f"💎 当前值: {config_item.value}",
            f"🏷️ 来源: {config_item.source.value}",
            f"📅 时间: {config_item.timestamp.strftime('%Y-%m-%d %H:%M:%S')}",
        ]
        
        if config_item.description:
            tooltip_lines.append(f"📝 描述: {config_item.description}")
        
        if conflicts:
            tooltip_lines.append("")
            tooltip_lines.append("⚠️ 冲突信息:")
            for conflict in conflicts:
                tooltip_lines.append(f"  • {conflict.get_conflict_summary()}")
        
        widget.setToolTip("\n".join(tooltip_lines))
    
    def create_context_menu(self, widget: QWidget, config_item: ConfigurationItem) -> QMenu:
        """创建右键上下文菜单
        
        Args:
            widget: 控件
            config_item: 配置项
            
        Returns:
            上下文菜单
        """
        menu = QMenu(widget)
        
        # 查看配置历史
        history_action = QAction("📜 查看配置历史", menu)
        history_action.triggered.connect(lambda: self._show_config_history(config_item))
        menu.addAction(history_action)
        
        # 重置到默认
        reset_action = QAction("🔄 重置到默认", menu)
        reset_action.triggered.connect(lambda: self._reset_to_default(config_item))
        menu.addAction(reset_action)
        
        # 复制配置
        copy_action = QAction("📋 复制配置", menu)
        copy_action.triggered.connect(lambda: self._copy_configuration(config_item))
        menu.addAction(copy_action)
        
        menu.addSeparator()
        
        # 编辑配置
        edit_action = QAction("✏️ 编辑配置", menu)
        edit_action.triggered.connect(lambda: self._edit_configuration(config_item))
        menu.addAction(edit_action)
        
        return menu
    
    def _add_blink_animation(self, widget: QWidget):
        """添加闪烁动画效果"""
        # 创建定时器实现闪烁效果
        if not hasattr(widget, '_blink_timer'):
            widget._blink_timer = QTimer()
            widget._blink_state = False
            
            def toggle_blink():
                widget._blink_state = not widget._blink_state
                if widget._blink_state:
                    widget.setStyleSheet(widget.styleSheet() + """
                        border: 3px solid #FF5722 !important;
                    """)
                else:
                    # 恢复原始样式
                    style = widget.styleSheet()
                    style = style.replace("border: 3px solid #FF5722 !important;", "")
                    widget.setStyleSheet(style)
            
            widget._blink_timer.timeout.connect(toggle_blink)
            widget._blink_timer.start(500)  # 每500ms闪烁一次
    
    def _darken_color(self, color_str: str, factor: float) -> str:
        """使颜色变暗"""
        color = QColor(color_str)
        h, s, l, a = color.getHsl()
        l = max(0, int(l * (1 - factor)))
        color.setHsl(h, s, l, a)
        return color.name()
    
    def _brighten_color(self, color_str: str, factor: float) -> str:
        """使颜色变亮"""
        color = QColor(color_str)
        h, s, l, a = color.getHsl()
        l = min(255, int(l * (1 + factor)))
        color.setHsl(h, s, l, a)
        return color.name()
    
    # 占位方法，后续实现具体功能
    def _show_config_history(self, config_item: ConfigurationItem):
        """显示配置历史"""
        # TODO: 实现配置历史查看
        self.logger.info(f"📜 查看配置历史: {config_item.key}")
    
    def _reset_to_default(self, config_item: ConfigurationItem):
        """重置到默认配置"""
        # TODO: 实现重置到默认
        self.logger.info(f"🔄 重置配置: {config_item.key}")
    
    def _copy_configuration(self, config_item: ConfigurationItem):
        """复制配置"""
        # TODO: 实现配置复制
        self.logger.info(f"📋 复制配置: {config_item.key}")
    
    def _edit_configuration(self, config_item: ConfigurationItem):
        """编辑配置"""
        # TODO: 实现配置编辑
        self.logger.info(f"✏️ 编辑配置: {config_item.key}")


if __name__ == "__main__":
    """测试可视化指示器"""
    import sys
    from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
    
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = QMainWindow()
    window.setWindowTitle("可视化指示器测试")
    window.resize(800, 600)
    
    central_widget = QWidget()
    layout = QVBoxLayout(central_widget)
    
    # 创建可视化指示器
    indicator = VisualSourceIndicator()
    
    # 创建测试标签
    for source in ConfigurationSource:
        label = indicator.create_source_indicator_label(source, f"测试配置 - {source.value}")
        layout.addWidget(label)
        
        priority_label = indicator.create_priority_indicator(source)
        layout.addWidget(priority_label)
    
    # 创建冲突指示器
    conflict_label = indicator.create_conflict_indicator([])
    layout.addWidget(conflict_label)
    
    window.setCentralWidget(central_widget)
    window.show()
    
    sys.exit(app.exec_())
