#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一配置界面用户反馈收集系统

提供以下功能：
- 用户反馈收集
- 使用体验评估
- 错误报告收集
- 功能使用统计
- 改进建议收集

创建时间: 2025-01-20
方案: 统一配置界面详细设计（方案3）- 阶段2实施
"""

import sys
import os
import json
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QTextEdit, QComboBox, QSpinBox, QCheckBox, QGroupBox,
    QProgressBar, QTabWidget, QWidget, QFormLayout,
    QMessageBox, QSlider, QButtonGroup, QRadioButton
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QColor, QPalette

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from src.utils.log_config import setup_logger


class FeedbackDialog(QDialog):
    """用户反馈对话框"""
    
    feedback_submitted = pyqtSignal(dict)  # 反馈提交信号
    
    def __init__(self, parent=None, feedback_type: str = "general"):
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        self.feedback_type = feedback_type
        self.feedback_data = {}
        
        self._init_ui()
        self._setup_feedback_type()
    
    def _init_ui(self):
        """初始化界面"""
        self.setWindowTitle("💬 用户反馈")
        self.setMinimumSize(500, 600)
        self.setModal(True)
        
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("感谢您的反馈！")
        title_label.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #2c3e50; margin: 20px;")
        layout.addWidget(title_label)
        
        # 创建选项卡
        self.tab_widget = QTabWidget()
        
        # 反馈类型选项卡
        self._create_feedback_tab()
        
        # 体验评估选项卡
        self._create_experience_tab()
        
        # 详细反馈选项卡
        self._create_details_tab()
        
        layout.addWidget(self.tab_widget)
        
        # 联系信息（可选）
        contact_group = QGroupBox("联系信息（可选）")
        contact_layout = QFormLayout(contact_group)
        
        self.contact_email = QTextEdit()
        self.contact_email.setMaximumHeight(30)
        self.contact_email.setPlaceholderText("您的邮箱（用于跟进反馈）")
        contact_layout.addRow("邮箱:", self.contact_email)
        
        layout.addWidget(contact_group)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        
        self.submit_btn = QPushButton("提交反馈")
        self.submit_btn.clicked.connect(self._submit_feedback)
        self.submit_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                font-size: 14px;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        
        button_layout.addWidget(self.cancel_btn)
        button_layout.addStretch()
        button_layout.addWidget(self.submit_btn)
        
        layout.addLayout(button_layout)
    
    def _create_feedback_tab(self):
        """创建反馈类型选项卡"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 反馈类型选择
        type_group = QGroupBox("反馈类型")
        type_layout = QVBoxLayout(type_group)
        
        self.feedback_type_group = QButtonGroup()
        
        feedback_types = [
            ("bug_report", "🐛 错误报告", "发现了程序错误或异常"),
            ("feature_request", "✨ 功能建议", "希望添加新功能或改进现有功能"),
            ("usability", "👥 易用性反馈", "界面操作或用户体验相关"),
            ("performance", "⚡ 性能问题", "程序运行速度或响应时间问题"),
            ("general", "💬 一般反馈", "其他意见或建议")
        ]
        
        for type_id, title, description in feedback_types:
            radio = QRadioButton(title)
            radio.setProperty("feedback_type", type_id)
            
            # 添加描述标签
            desc_label = QLabel(f"  {description}")
            desc_label.setStyleSheet("color: #7f8c8d; font-size: 11px; margin-left: 20px;")
            
            type_layout.addWidget(radio)
            type_layout.addWidget(desc_label)
            
            self.feedback_type_group.addButton(radio)
            
            if type_id == self.feedback_type:
                radio.setChecked(True)
        
        layout.addWidget(type_group)
        
        # 严重程度（仅错误报告）
        self.severity_group = QGroupBox("严重程度")
        severity_layout = QVBoxLayout(self.severity_group)
        
        self.severity_combo = QComboBox()
        self.severity_combo.addItems([
            "低 - 轻微问题，不影响使用",
            "中 - 影响部分功能，有替代方案",
            "高 - 影响主要功能，无替代方案",
            "严重 - 程序崩溃或无法使用"
        ])
        severity_layout.addWidget(self.severity_combo)
        
        layout.addWidget(self.severity_group)
        
        self.tab_widget.addTab(tab, "反馈类型")
    
    def _create_experience_tab(self):
        """创建体验评估选项卡"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 总体满意度
        satisfaction_group = QGroupBox("总体满意度")
        satisfaction_layout = QVBoxLayout(satisfaction_group)
        
        self.satisfaction_slider = QSlider(Qt.Horizontal)
        self.satisfaction_slider.setRange(1, 10)
        self.satisfaction_slider.setValue(7)
        self.satisfaction_slider.setTickPosition(QSlider.TicksBelow)
        self.satisfaction_slider.setTickInterval(1)
        
        self.satisfaction_label = QLabel("7 - 满意")
        self.satisfaction_label.setAlignment(Qt.AlignCenter)
        
        self.satisfaction_slider.valueChanged.connect(
            lambda v: self.satisfaction_label.setText(f"{v} - {self._get_satisfaction_text(v)}")
        )
        
        satisfaction_layout.addWidget(QLabel("不满意 (1) ←→ 非常满意 (10)"))
        satisfaction_layout.addWidget(self.satisfaction_slider)
        satisfaction_layout.addWidget(self.satisfaction_label)
        
        layout.addWidget(satisfaction_group)
        
        # 功能评估
        features_group = QGroupBox("功能评估")
        features_layout = QFormLayout(features_group)
        
        self.feature_ratings = {}
        features = [
            ("smart_mapping", "智能字段映射"),
            ("visual_indicators", "可视化配置来源"),
            ("conflict_resolution", "冲突检测与解决"),
            ("data_preview", "数据预览功能"),
            ("validation", "配置验证"),
            ("overall_ui", "整体界面设计")
        ]
        
        for feature_id, feature_name in features:
            slider = QSlider(Qt.Horizontal)
            slider.setRange(1, 5)
            slider.setValue(4)
            slider.setTickPosition(QSlider.TicksBelow)
            slider.setTickInterval(1)
            
            label = QLabel("4 - 好")
            label.setMinimumWidth(80)
            
            slider.valueChanged.connect(
                lambda v, l=label: l.setText(f"{v} - {self._get_rating_text(v)}")
            )
            
            row_layout = QHBoxLayout()
            row_layout.addWidget(slider)
            row_layout.addWidget(label)
            
            features_layout.addRow(f"{feature_name}:", row_layout)
            self.feature_ratings[feature_id] = slider
        
        layout.addWidget(features_group)
        
        self.tab_widget.addTab(tab, "体验评估")
    
    def _create_details_tab(self):
        """创建详细反馈选项卡"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 详细描述
        desc_group = QGroupBox("详细描述")
        desc_layout = QVBoxLayout(desc_group)
        
        self.description_text = QTextEdit()
        self.description_text.setPlaceholderText(
            "请详细描述您的问题、建议或意见...\n\n"
            "如果是错误报告，请包含：\n"
            "- 问题出现的具体步骤\n"
            "- 期望的结果和实际结果\n"
            "- 相关的错误信息（如有）\n\n"
            "如果是功能建议，请包含：\n"
            "- 希望添加的功能描述\n"
            "- 使用场景和必要性\n"
            "- 期望的实现方式（可选）"
        )
        desc_layout.addWidget(self.description_text)
        
        layout.addWidget(desc_group)
        
        # 使用环境信息
        env_group = QGroupBox("环境信息")
        env_layout = QFormLayout(env_group)
        
        self.include_env_info = QCheckBox("包含系统和软件环境信息")
        self.include_env_info.setChecked(True)
        self.include_env_info.setToolTip("包含操作系统、Python版本等信息，有助于问题诊断")
        
        env_layout.addRow(self.include_env_info)
        
        layout.addWidget(env_group)
        
        # 改进建议
        suggestions_group = QGroupBox("改进建议")
        suggestions_layout = QVBoxLayout(suggestions_group)
        
        self.suggestions_text = QTextEdit()
        self.suggestions_text.setMaximumHeight(100)
        self.suggestions_text.setPlaceholderText("您对产品改进有什么建议吗？")
        suggestions_layout.addWidget(self.suggestions_text)
        
        layout.addWidget(suggestions_group)
        
        self.tab_widget.addTab(tab, "详细反馈")
    
    def _setup_feedback_type(self):
        """根据反馈类型设置界面"""
        if self.feedback_type == "bug_report":
            self.severity_group.setVisible(True)
        else:
            self.severity_group.setVisible(False)
    
    def _get_satisfaction_text(self, value: int) -> str:
        """获取满意度文本"""
        texts = {
            1: "非常不满意", 2: "不满意", 3: "比较不满意", 4: "一般", 5: "还可以",
            6: "比较满意", 7: "满意", 8: "比较满意", 9: "很满意", 10: "非常满意"
        }
        return texts.get(value, "一般")
    
    def _get_rating_text(self, value: int) -> str:
        """获取评分文本"""
        texts = {1: "差", 2: "一般", 3: "好", 4: "很好", 5: "优秀"}
        return texts.get(value, "一般")
    
    def _submit_feedback(self):
        """提交反馈"""
        try:
            # 收集反馈数据
            feedback_data = self._collect_feedback_data()
            
            # 验证必填项
            if not feedback_data.get("description", "").strip():
                QMessageBox.warning(self, "提示", "请填写详细描述！")
                self.tab_widget.setCurrentIndex(2)  # 切换到详细反馈选项卡
                self.description_text.setFocus()
                return
            
            # 提交反馈
            self.feedback_submitted.emit(feedback_data)
            
            QMessageBox.information(self, "感谢", "感谢您的反馈！我们将认真考虑您的意见。")
            self.accept()
            
        except Exception as e:
            self.logger.error(f"❌ 提交反馈失败: {e}")
            QMessageBox.critical(self, "错误", f"提交反馈失败: {e}")
    
    def _collect_feedback_data(self) -> Dict[str, Any]:
        """收集反馈数据"""
        data = {
            "feedback_id": str(uuid.uuid4()),
            "timestamp": datetime.now().isoformat(),
            "feedback_type": self._get_selected_feedback_type(),
            "description": self.description_text.toPlainText().strip(),
            "suggestions": self.suggestions_text.toPlainText().strip(),
            "contact_email": self.contact_email.toPlainText().strip(),
            "satisfaction": self.satisfaction_slider.value(),
            "feature_ratings": {
                feature_id: slider.value() 
                for feature_id, slider in self.feature_ratings.items()
            }
        }
        
        # 添加严重程度（仅错误报告）
        if data["feedback_type"] == "bug_report":
            data["severity"] = self.severity_combo.currentIndex() + 1
        
        # 添加环境信息（如果用户同意）
        if self.include_env_info.isChecked():
            data["environment"] = self._collect_environment_info()
        
        return data
    
    def _get_selected_feedback_type(self) -> str:
        """获取选择的反馈类型"""
        for button in self.feedback_type_group.buttons():
            if button.isChecked():
                return button.property("feedback_type")
        return "general"
    
    def _collect_environment_info(self) -> Dict[str, str]:
        """收集环境信息"""
        import sys
        import platform
        
        return {
            "os": platform.system(),
            "os_version": platform.version(),
            "python_version": sys.version,
            "qt_version": "PyQt5",  # 可以动态获取
            "app_version": "1.0.0"  # 可以从配置获取
        }


class UsageAnalytics:
    """使用情况分析"""
    
    def __init__(self):
        self.logger = setup_logger(__name__)
        self.analytics_file = "state/user/usage_analytics.json"
        self.session_start_time = datetime.now()
        self.usage_data = self._load_usage_data()
    
    def track_feature_usage(self, feature_name: str, action: str = "used"):
        """跟踪功能使用"""
        try:
            today = datetime.now().date().isoformat()
            
            if "feature_usage" not in self.usage_data:
                self.usage_data["feature_usage"] = {}
            
            if today not in self.usage_data["feature_usage"]:
                self.usage_data["feature_usage"][today] = {}
            
            if feature_name not in self.usage_data["feature_usage"][today]:
                self.usage_data["feature_usage"][today][feature_name] = 0
            
            self.usage_data["feature_usage"][today][feature_name] += 1
            
            self.logger.debug(f"📊 功能使用记录: {feature_name} - {action}")
            
        except Exception as e:
            self.logger.error(f"❌ 跟踪功能使用失败: {e}")
    
    def track_session_time(self):
        """跟踪会话时间"""
        try:
            session_duration = (datetime.now() - self.session_start_time).total_seconds()
            today = datetime.now().date().isoformat()
            
            if "session_times" not in self.usage_data:
                self.usage_data["session_times"] = {}
            
            if today not in self.usage_data["session_times"]:
                self.usage_data["session_times"][today] = []
            
            self.usage_data["session_times"][today].append(session_duration)
            
            self.logger.debug(f"📊 会话时间记录: {session_duration:.1f}秒")
            
        except Exception as e:
            self.logger.error(f"❌ 跟踪会话时间失败: {e}")
    
    def track_error_occurrence(self, error_type: str, error_message: str):
        """跟踪错误发生"""
        try:
            today = datetime.now().date().isoformat()
            
            if "errors" not in self.usage_data:
                self.usage_data["errors"] = {}
            
            if today not in self.usage_data["errors"]:
                self.usage_data["errors"][today] = []
            
            error_record = {
                "timestamp": datetime.now().isoformat(),
                "error_type": error_type,
                "error_message": error_message
            }
            
            self.usage_data["errors"][today].append(error_record)
            
            self.logger.debug(f"📊 错误记录: {error_type}")
            
        except Exception as e:
            self.logger.error(f"❌ 跟踪错误发生失败: {e}")
    
    def get_usage_statistics(self) -> Dict[str, Any]:
        """获取使用统计"""
        try:
            today = datetime.now().date().isoformat()
            
            # 今日功能使用统计
            today_features = self.usage_data.get("feature_usage", {}).get(today, {})
            
            # 今日会话时间
            today_sessions = self.usage_data.get("session_times", {}).get(today, [])
            total_session_time = sum(today_sessions)
            avg_session_time = total_session_time / len(today_sessions) if today_sessions else 0
            
            # 今日错误统计
            today_errors = self.usage_data.get("errors", {}).get(today, [])
            
            return {
                "today_feature_usage": today_features,
                "today_session_count": len(today_sessions),
                "today_total_time": total_session_time,
                "today_avg_session_time": avg_session_time,
                "today_error_count": len(today_errors),
                "most_used_feature": max(today_features.items(), key=lambda x: x[1])[0] if today_features else None
            }
            
        except Exception as e:
            self.logger.error(f"❌ 获取使用统计失败: {e}")
            return {}
    
    def save_usage_data(self):
        """保存使用数据"""
        try:
            os.makedirs(os.path.dirname(self.analytics_file), exist_ok=True)
            with open(self.analytics_file, 'w', encoding='utf-8') as f:
                json.dump(self.usage_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"❌ 保存使用数据失败: {e}")
    
    def _load_usage_data(self) -> Dict[str, Any]:
        """加载使用数据"""
        try:
            if os.path.exists(self.analytics_file):
                with open(self.analytics_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            self.logger.warning(f"⚠️ 加载使用数据失败: {e}")
        
        return {}


class FeedbackManager:
    """反馈管理器"""
    
    def __init__(self):
        self.logger = setup_logger(__name__)
        self.feedback_file = "state/user/feedback_data.json"
        self.analytics = UsageAnalytics()
        self.feedback_data = self._load_feedback_data()
    
    def show_feedback_dialog(self, parent=None, feedback_type: str = "general") -> bool:
        """显示反馈对话框"""
        try:
            dialog = FeedbackDialog(parent, feedback_type)
            dialog.feedback_submitted.connect(self._handle_feedback_submission)
            
            result = dialog.exec_()
            return result == QDialog.Accepted
            
        except Exception as e:
            self.logger.error(f"❌ 显示反馈对话框失败: {e}")
            return False
    
    def collect_automatic_feedback(self, event_type: str, data: Dict[str, Any]):
        """收集自动反馈"""
        try:
            feedback_record = {
                "feedback_id": str(uuid.uuid4()),
                "timestamp": datetime.now().isoformat(),
                "type": "automatic",
                "event_type": event_type,
                "data": data
            }
            
            self.feedback_data.append(feedback_record)
            self._save_feedback_data()
            
            self.logger.debug(f"📊 自动反馈收集: {event_type}")
            
        except Exception as e:
            self.logger.error(f"❌ 收集自动反馈失败: {e}")
    
    def _handle_feedback_submission(self, feedback_data: Dict[str, Any]):
        """处理反馈提交"""
        try:
            # 保存反馈数据
            self.feedback_data.append(feedback_data)
            self._save_feedback_data()
            
            # 记录功能使用
            self.analytics.track_feature_usage("feedback_submission")
            
            self.logger.info(f"✅ 用户反馈已保存: {feedback_data.get('feedback_type', '未知类型')}")
            
        except Exception as e:
            self.logger.error(f"❌ 处理反馈提交失败: {e}")
    
    def get_feedback_summary(self) -> Dict[str, Any]:
        """获取反馈摘要"""
        try:
            total_feedback = len(self.feedback_data)
            
            # 按类型统计
            type_counts = {}
            satisfaction_scores = []
            
            for feedback in self.feedback_data:
                feedback_type = feedback.get("feedback_type", "unknown")
                type_counts[feedback_type] = type_counts.get(feedback_type, 0) + 1
                
                if "satisfaction" in feedback:
                    satisfaction_scores.append(feedback["satisfaction"])
            
            avg_satisfaction = sum(satisfaction_scores) / len(satisfaction_scores) if satisfaction_scores else 0
            
            return {
                "total_feedback": total_feedback,
                "feedback_by_type": type_counts,
                "average_satisfaction": avg_satisfaction,
                "usage_statistics": self.analytics.get_usage_statistics()
            }
            
        except Exception as e:
            self.logger.error(f"❌ 获取反馈摘要失败: {e}")
            return {}
    
    def _load_feedback_data(self) -> List[Dict[str, Any]]:
        """加载反馈数据"""
        try:
            if os.path.exists(self.feedback_file):
                with open(self.feedback_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            self.logger.warning(f"⚠️ 加载反馈数据失败: {e}")
        
        return []
    
    def _save_feedback_data(self):
        """保存反馈数据"""
        try:
            os.makedirs(os.path.dirname(self.feedback_file), exist_ok=True)
            with open(self.feedback_file, 'w', encoding='utf-8') as f:
                json.dump(self.feedback_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"❌ 保存反馈数据失败: {e}")


# 全局反馈管理器实例
_feedback_manager = None

def get_feedback_manager() -> FeedbackManager:
    """获取全局反馈管理器实例"""
    global _feedback_manager
    if _feedback_manager is None:
        _feedback_manager = FeedbackManager()
    return _feedback_manager


if __name__ == "__main__":
    """测试反馈系统"""
    from PyQt5.QtWidgets import QApplication
    
    app = QApplication([])
    
    # 测试反馈对话框
    feedback_mgr = get_feedback_manager()
    result = feedback_mgr.show_feedback_dialog(feedback_type="feature_request")
    
    print(f"反馈对话框结果: {result}")
    
    # 测试使用分析
    analytics = UsageAnalytics()
    analytics.track_feature_usage("smart_mapping")
    analytics.track_feature_usage("data_preview")
    
    stats = analytics.get_usage_statistics()
    print(f"使用统计: {stats}")
    
    # 获取反馈摘要
    summary = feedback_mgr.get_feedback_summary()
    print(f"反馈摘要: {summary}")
    
    app.quit()
